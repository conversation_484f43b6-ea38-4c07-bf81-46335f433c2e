import { NextRequest } from "next/server"
import { Message, streamText, tool } from "ai"
import { z } from "zod"

import { AdvancedSemanticSearch } from "@/lib/ai/advanced-search"
import { semanticSearch, semanticSearchInDocumentChunks } from "@/lib/ai/embeddings"
import { TargetedDocumentSearch } from "@/lib/ai/targeted-document-search"
import { prisma } from "@/lib/prisma"
import { openai } from "@ai-sdk/openai"
import { logger } from "@buildismart/lib"

// Types for search results
interface DocumentResult {
  originalFileName: string
  content: string
  similarity: number
}

interface ChunkResult {
  originalFileName: string
  content: string
  similarity: number
  pageNumber?: number
}

export const maxDuration = 150

// Fonction pour gérer le chat général (sans projet)
async function handleGeneralChat(messages: Message[]) {
  const systemPrompt = `Tu es BuildiSmart AI, un assistant IA spécialisé dans la construction et le BTP.

Tu peux aider avec:
- Questions générales sur la construction
- Conseils techniques
- Réglementations du bâtiment
- Matériaux de construction
- Méthodes de construction
- Sécurité sur les chantiers

Réponds de manière professionnelle et précise. Si une question nécessite l'analyse de documents spécifiques, suggère à l'utilisateur de créer un projet et d'uploader ses documents.`

  try {
    const result = await streamText({
      model: openai("gpt-3.5-turbo"),
      system: systemPrompt,
      messages,
      temperature: 0.7, // Un peu plus créatif pour le chat général
    })

    logger.log("General chat response generated", {
      messagesCount: messages.length,
    })

    return result.toDataStreamResponse()
  } catch (error) {
    logger.error("General chat request failed", { error })
    return new Response(
      `Erreur lors du traitement de la requête: ${error instanceof Error ? error.message : "Erreur inconnue"}`,
      { status: 500 }
    )
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { messages, projectId, ...otherParams } = body as {
      messages: Message[]
      projectId?: string
      [key: string]: unknown
    }

    if (!messages) {
      return new Response("Requête invalide: messages manquant", { status: 400 })
    }

    logger.log("Chat request received", {
      projectId: projectId || "general",
      messagesCount: messages.length,
      otherParams: Object.keys(otherParams),
    })

    // Si pas de projectId, c'est un chat général
    if (!projectId) {
      return handleGeneralChat(messages)
    }

    // Validate project exists and get essential context only
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      select: {
        id: true,
        name: true,
        projectSummary: true,
        createdAt: true,
        allProjectDocuments: {
          where: { status: "READY" },
          select: {
            id: true,
            originalFileName: true,
            mimeType: true,
            createdAt: true,
            fileSummary: true,
          },
          orderBy: { createdAt: "asc" },
        },
      },
    })

    if (!project) {
      return new Response("Projet non trouvé", { status: 404 })
    }

    // Build lightweight initial context (NO automatic search)
    const initialContext = `**Projet:** ${project.name}
${project.projectSummary ? `**Résumé:** ${project.projectSummary}` : ""}

**Documents disponibles (${project.allProjectDocuments.length} traités):**
${
  project.allProjectDocuments.length > 0
    ? project.allProjectDocuments
        .map((doc) => {
          const fileType = doc.mimeType.includes("pdf")
            ? "📄 PDF"
            : doc.mimeType.includes("word")
              ? "📝 Word"
              : doc.mimeType.includes("image")
                ? "🖼️ Image"
                : "📁 Fichier"
          return `• ${fileType} ${doc.originalFileName}${doc.fileSummary ? ` - ${doc.fileSummary.substring(0, 100)}...` : ""}`
        })
        .join("\n")
    : "Aucun document traité disponible"
}

**Instructions:** Utilisez les outils de recherche pour accéder au contenu spécifique des documents selon les besoins de l'utilisateur.`

    // Enhanced tools for construction-specific queries
    const deepSearchTool = tool({
      description: "🔍 Recherche approfondie dans les extraits détaillés des documents de construction",
      parameters: z.object({
        query: z.string().describe("La question technique ou spécifique à rechercher"),
        focus: z
          .enum(["technique", "prix", "planning", "normes", "materiaux"])
          .optional()
          .describe("Type de focus pour la recherche"),
      }),
      execute: async ({ query, focus }) => {
        logger.log("Deep search tool called", { query, focus, projectId })

        const enhancedQuery = focus
          ? `${query} ${
              focus === "technique"
                ? "spécifications techniques"
                : focus === "prix"
                  ? "coûts prix devis"
                  : focus === "planning"
                    ? "délais planning échéances"
                    : focus === "normes"
                      ? "normes réglementation DTU"
                      : "matériaux fournitures"
            }`
          : query

        const {
          chunks = [],
          totalChunks,
          totalDocuments,
        } = await semanticSearchInDocumentChunks(projectId, enhancedQuery, 25) // Increased for better coverage

        if (Array.isArray(chunks) && chunks.length > 0) {
          const formattedChunks = (chunks as ChunkResult[])
            .map(
              (chunk) =>
                `📄 **${chunk.originalFileName}** (page ${chunk.pageNumber || "N/A"}, similarité: ${(chunk.similarity * 100).toFixed(1)}%)\n${chunk.content}`
            )
            .join("\n\n")

          return {
            status: `✅ Trouvé ${chunks.length} extraits pertinents sur ${totalChunks} dans ${totalDocuments} documents`,
            context: formattedChunks,
            metadata: { chunksFound: chunks.length, totalChunks, totalDocuments, focus },
          }
        }

        return {
          status: "❌ Aucun extrait détaillé trouvé pour cette recherche",
          context: "Aucun contenu spécifique trouvé dans les documents.",
          metadata: { chunksFound: 0, totalChunks, totalDocuments, focus },
        }
      },
    })

    const projectAnalysisTool = tool({
      description: "📊 Analyse globale du projet de construction avec synthèse des documents",
      parameters: z.object({
        analysisType: z
          .enum(["overview", "technical", "financial", "regulatory"])
          .describe("Type d'analyse à effectuer"),
      }),
      execute: async ({ analysisType }) => {
        logger.log("Project analysis tool called", { analysisType, projectId })

        try {
          const documents = await prisma.projectDocument.findMany({
            where: { projectId, status: "READY" },
            select: {
              originalFileName: true,
              fileSummary: true,
              mimeType: true,
              createdAt: true,
            },
            orderBy: { createdAt: "asc" },
          })

          if (documents.length === 0) {
            return {
              status: "❌ Aucun document analysé disponible",
              analysis: "Le projet ne contient pas encore de documents traités.",
              metadata: { documentsCount: 0, analysisType },
            }
          }

          // Analysis type is used in the response formatting

          const documentsSummary = documents
            .map((doc) => `• ${doc.originalFileName}: ${doc.fileSummary || "Pas de résumé"}`)
            .join("\n")

          return {
            status: `✅ Analyse ${analysisType} générée pour ${documents.length} documents`,
            analysis: `**Analyse ${analysisType} du projet "${project.name}":**\n\n${documentsSummary}`,
            metadata: {
              documentsCount: documents.length,
              analysisType,
              documentTypes: [...new Set(documents.map((d) => d.mimeType))],
            },
          }
        } catch (error) {
          logger.error("Project analysis failed", { error, projectId, analysisType })
          return {
            status: "❌ Erreur lors de l'analyse du projet",
            analysis: "Une erreur est survenue pendant l'analyse.",
            metadata: { error: true, analysisType },
          }
        }
      },
    })

    const documentSearchTool = tool({
      description: "📋 Recherche ciblée dans des types de documents spécifiques (CCTP, DPGF, plans, etc.)",
      parameters: z.object({
        query: z.string().describe("Terme ou concept à rechercher"),
        documentType: z
          .enum(["CCTP", "DPGF", "plans", "devis", "tous"])
          .optional()
          .describe("Type de document à cibler"),
      }),
      execute: async ({ query, documentType }) => {
        logger.log("Document search tool called", { query, documentType, projectId })

        const typeFilter = documentType && documentType !== "tous" ? documentType.toLowerCase() : null

        const { documents = [], documentsCount } = await semanticSearch(query, projectId, 15) // Increased for better coverage

        let filteredDocuments = Array.isArray(documents) ? documents : []

        if (typeFilter) {
          filteredDocuments = (filteredDocuments as DocumentResult[]).filter(
            (doc) =>
              doc.originalFileName.toLowerCase().includes(typeFilter) ||
              (doc.content && doc.content.toLowerCase().includes(typeFilter))
          )
        }

        if (filteredDocuments.length > 0) {
          const formattedDocs = (filteredDocuments as DocumentResult[])
            .map(
              (doc) =>
                `📄 **${doc.originalFileName}** (similarité: ${(doc.similarity * 100).toFixed(1)}%)\n${doc.content}`
            )
            .join("\n\n")

          return {
            status: `✅ Trouvé ${filteredDocuments.length} documents${typeFilter ? ` de type ${documentType}` : ""} sur ${documentsCount}`,
            documents: formattedDocs,
            metadata: {
              documentsFound: filteredDocuments.length,
              totalDocuments: documentsCount,
              documentType: documentType || "tous",
            },
          }
        }

        return {
          status: `❌ Aucun document${typeFilter ? ` de type ${documentType}` : ""} trouvé pour "${query}"`,
          documents: "Aucun document correspondant trouvé.",
          metadata: {
            documentsFound: 0,
            totalDocuments: documentsCount,
            documentType: documentType || "tous",
          },
        }
      },
    })

    // Enhanced system prompt for construction expertise
    const systemPrompt = `Tu es BuildiSmart, un assistant IA expert en construction et BTP (Bâtiment et Travaux Publics).

**Ton rôle :** Analyser les documents de consultation d'entreprise (DCE) et aider les professionnels du BTP à comprendre les projets de construction.

**Tes capacités :**
- 🔍 Recherche approfondie dans les documents techniques
- 📊 Analyse de projets de construction
- 📋 Recherche ciblée par type de document
- 💡 Expertise en normes, réglementations et techniques BTP

**Instructions importantes :**
1. Base-toi UNIQUEMENT sur le contexte fourni et les outils disponibles
2. Si l'information n'est pas suffisante, utilise les outils pour chercher plus de détails
3. Indique toujours tes sources (nom du document, page si disponible)
4. Utilise un langage technique approprié au secteur BTP
5. Signale quand tu utilises un outil avec un message informatif

**Outils disponibles :**
- 🔍 **Recherche approfondie** : Pour les détails techniques spécifiques
- 📊 **Analyse de projet** : Pour une vue d'ensemble ou analyse thématique
- 📋 **Recherche par type** : Pour cibler CCTP, DPGF, plans, devis, etc.
- 🎯 **Identification de documents** : Identifier des documents spécifiques par nom/type (NOUVEAU)
- 🔍 **Recherche exhaustive** : Analyser TOUS les chunks d'un document spécifique (NOUVEAU)
- 📊 **Recherche d'informations spécifiques** : Extraire prix, dates, dimensions, spécifications (NOUVEAU)
- 🎯 **Recherche de précision avancée** : Compréhension contextuelle avec attribution exacte des sources
- 🔍 **Recherche exacte** : Pour trouver des phrases, mots-clés ou détails précis

**INSTRUCTIONS CRITIQUES pour les recherches ciblées :**
1. **Pour des questions sur un document spécifique** (ex: "que dit le CCTP sur le béton ?"):
   - D'abord utiliser l'outil d'identification de documents
   - Puis utiliser la recherche exhaustive avec l'ID du document trouvé
2. **Pour extraire des informations précises** (prix, dates, spécifications):
   - Utiliser l'outil de recherche d'informations spécifiques
3. **TOUJOURS examiner TOUS les chunks** d'un document identifié pour garantir l'exhaustivité

**Projet actuel :** ${project.name}
**Documents disponibles :** ${project.allProjectDocuments.length} documents traités

**Contexte initial :**
${initialContext}

Réponds de manière précise et professionnelle en utilisant les outils si nécessaire.`

    // Targeted document search tools
    const documentIdentificationTool = tool({
      description: "🎯 Identifier des documents spécifiques par nom, type ou contexte pour une recherche ciblée",
      parameters: z.object({
        query: z.string().describe("Description du document recherché (ex: 'CCTP', 'devis', 'plan architecture')"),
        context: z.string().optional().describe("Contexte supplémentaire pour affiner l'identification"),
      }),
      execute: async ({ query, context }) => {
        logger.log("Document identification tool called", { query, context, projectId })

        const targetedSearch = new TargetedDocumentSearch()
        const identifications = await targetedSearch.identifyTargetDocuments(projectId, query, context)

        if (identifications.length === 0) {
          return `Aucun document trouvé correspondant à "${query}". Vérifiez que les documents sont bien uploadés et traités.`
        }

        const results = identifications
          .slice(0, 10)
          .map((id, index) => {
            const confidenceBar = "█".repeat(Math.round(id.confidence * 5))
            return (
              `${index + 1}. **${id.documentName}** (${id.documentType})\n` +
              `   📊 Confiance: ${confidenceBar} (${Math.round(id.confidence * 100)}%)\n` +
              `   💡 Raison: ${id.matchReason}\n` +
              `   🆔 ID: ${id.documentId}\n`
            )
          })
          .join("\n")

        return (
          `🎯 **Documents identifiés pour:** "${query}"\n\n${results}\n` +
          `💡 **Conseil:** Utilisez l'outil de recherche exhaustive avec l'ID du document pour chercher des informations spécifiques.`
        )
      },
    })

    const exhaustiveDocumentSearchTool = tool({
      description: "🔍 Recherche exhaustive dans un document spécifique par ID - examine TOUS les chunks du document",
      parameters: z.object({
        documentId: z.string().describe("ID du document à analyser (obtenu via l'outil d'identification)"),
        searchQuery: z.string().describe("Information spécifique à rechercher dans le document"),
        searchType: z
          .enum(["exact", "fuzzy", "comprehensive"])
          .optional()
          .describe("Type de recherche (comprehensive par défaut)"),
      }),
      execute: async ({ documentId, searchQuery, searchType = "comprehensive" }) => {
        logger.log("Exhaustive document search tool called", { documentId, searchQuery, searchType, projectId })

        const targetedSearch = new TargetedDocumentSearch()

        try {
          const result = await targetedSearch.searchWithinDocument(documentId, searchQuery, searchType)

          if (result.matches.length === 0) {
            return (
              `❌ Aucune correspondance trouvée pour "${searchQuery}" dans ${result.documentName}.\n` +
              `📊 Document analysé: ${result.totalChunks} chunks examinés.\n` +
              `💡 Essayez une recherche plus générale ou vérifiez l'orthographe.`
            )
          }

          const matchSummary = result.matches
            .slice(0, 15)
            .map((match, index) => {
              const matchIcon =
                {
                  exact: "🎯",
                  partial: "🔍",
                  contextual: "🔗",
                }[match.matchType] || "📄"

              const scoreBar = "█".repeat(Math.round(match.matchScore * 5))

              return (
                `${index + 1}. ${matchIcon} **Page ${match.pageNumber}** (Score: ${scoreBar} ${Math.round(match.matchScore * 100)}%)\n` +
                `   📝 **Contenu trouvé:**\n` +
                `   "${match.extractedText}"\n` +
                `   🔗 **Contenu complet avec surlignage:**\n` +
                `   ${match.highlightedContent.substring(0, 300)}${match.highlightedContent.length > 300 ? "..." : ""}\n`
              )
            })
            .join("\n")

          return (
            `🔍 **Recherche exhaustive dans:** ${result.documentName}\n` +
            `📊 **${result.matches.length} correspondances trouvées** sur ${result.totalChunks} chunks analysés\n` +
            `🔍 **Type de recherche:** ${searchType}\n\n` +
            matchSummary
          )
        } catch (error) {
          logger.error("Exhaustive document search failed", { error, documentId, searchQuery })
          return `❌ Erreur lors de la recherche dans le document: ${error instanceof Error ? error.message : "Erreur inconnue"}`
        }
      },
    })

    const specificInformationSearchTool = tool({
      description: "📊 Recherche d'informations spécifiques (prix, dates, dimensions, spécifications) dans un document",
      parameters: z.object({
        documentId: z.string().describe("ID du document à analyser"),
        informationType: z
          .enum(["numbers", "dates", "specifications", "prices", "dimensions"])
          .describe("Type d'information à extraire"),
        context: z.string().optional().describe("Contexte pour filtrer les résultats (ex: 'béton', 'fondation')"),
      }),
      execute: async ({ documentId, informationType, context }) => {
        logger.log("Specific information search tool called", { documentId, informationType, context, projectId })

        const targetedSearch = new TargetedDocumentSearch()

        try {
          const matches = await targetedSearch.searchSpecificInformation(documentId, informationType, context)

          if (matches.length === 0) {
            return `❌ Aucune information de type "${informationType}" trouvée dans le document.`
          }

          // Get document name
          const document = await prisma.projectDocument.findUnique({
            where: { id: documentId },
            select: { originalFileName: true },
          })

          const typeLabels = {
            numbers: "Nombres et mesures",
            dates: "Dates",
            specifications: "Spécifications techniques",
            prices: "Prix et coûts",
            dimensions: "Dimensions",
          }

          const results = matches
            .slice(0, 20)
            .map((match, index) => {
              return (
                `${index + 1}. **Page ${match.pageNumber}**\n` +
                `   📊 **Trouvé:** ${match.extractedText}\n` +
                `   📝 **Contexte:** ${match.content.substring(0, 200)}...\n`
              )
            })
            .join("\n")

          return (
            `📊 **${typeLabels[informationType]} trouvées dans:** ${document?.originalFileName || "Document"}\n` +
            `🔍 **${matches.length} éléments trouvés**${context ? ` (contexte: ${context})` : ""}\n\n` +
            results
          )
        } catch (error) {
          logger.error("Specific information search failed", { error, documentId, informationType })
          return `❌ Erreur lors de la recherche d'informations spécifiques: ${error instanceof Error ? error.message : "Erreur inconnue"}`
        }
      },
    })

    // Advanced precision search tool using multi-strategy approach
    const advancedPrecisionSearchTool = tool({
      description:
        "🎯 Recherche de précision avancée avec compréhension contextuelle et attribution exacte des sources",
      parameters: z.object({
        query: z.string().describe("Requête de recherche (peut être une phrase, concept, ou question spécifique)"),
        domain: z
          .enum(["construction", "technical", "financial", "planning", "general"])
          .optional()
          .describe("Domaine spécialisé pour améliorer la recherche contextuelle"),
        requireExact: z
          .boolean()
          .optional()
          .describe("Exiger des correspondances exactes (true) ou permettre la recherche sémantique (false)"),
      }),
      execute: async ({ query, domain = "construction", requireExact = false }) => {
        logger.log("Advanced precision search tool called", { query, domain, requireExact, projectId })

        const advancedSearch = new AdvancedSemanticSearch()

        // Configure search strategies based on requirements
        const searchStrategies = requireExact ? ["exact", "keyword"] : ["exact", "semantic", "contextual", "keyword"]

        const results = await advancedSearch.performAdvancedSearch(projectId, query, {
          maxResults: 20,
          minSimilarity: requireExact ? 0.8 : 0.3,
          includeContext: true,
          searchStrategies: searchStrategies as ("exact" | "semantic" | "contextual" | "keyword")[],
          domainFocus: domain,
        })

        if (results.length === 0) {
          return `Aucun résultat trouvé pour "${query}". Essayez de reformuler votre recherche ou d'utiliser des termes plus généraux.`
        }

        // Format results with precise attribution
        const formattedResults = results
          .slice(0, 15)
          .map((result, index) => {
            const matchTypeIcon =
              {
                exact: "🎯",
                semantic: "🔍",
                contextual: "🔗",
                keyword: "🔤",
              }[result.matchType] || "📄"

            const relevanceBar = "█".repeat(Math.round(result.relevanceScore * 5))

            let output = `${index + 1}. ${matchTypeIcon} **${result.documentName}** (page ${result.pageNumber})`

            if (result.section) {
              output += ` - Section: ${result.section}`
            }

            output += `\n   📊 Pertinence: ${relevanceBar} (${Math.round(result.relevanceScore * 100)}%)\n`

            // Show extracted passages with highlighting
            if (result.extractedPassages.length > 0) {
              output += `   📝 **Contenu trouvé:**\n`
              result.extractedPassages.slice(0, 2).forEach((passage) => {
                output += `   "${passage}"\n`
              })
            }

            // Add context if available
            if (result.contextBefore || result.contextAfter) {
              output += `   🔗 **Contexte disponible** (utilisez l'outil de recherche approfondie pour plus de détails)\n`
            }

            return output + "\n"
          })
          .join("")

        const summary =
          `🎯 **Recherche avancée pour:** "${query}"\n` +
          `📊 **${results.length} résultats trouvés** avec attribution précise des sources\n` +
          `🔍 **Stratégies utilisées:** ${searchStrategies.join(", ")}\n\n` +
          formattedResults

        return summary
      },
    })

    // Enhanced exact search tool for granular retrieval
    const exactSearchTool = tool({
      description: "🔍 Recherche exacte de phrases, mots-clés ou détails spécifiques dans les documents",
      parameters: z.object({
        phrase: z.string().describe("Phrase exacte, mot-clé ou détail spécifique à rechercher"),
        context: z.string().optional().describe("Contexte supplémentaire pour affiner la recherche"),
      }),
      execute: async ({ phrase, context }) => {
        logger.log("Exact search tool called", { phrase, context, projectId })

        // Combine phrase with context for better search
        const searchQuery = context ? `${phrase} ${context}` : phrase

        // Use enhanced chunk search with higher limit for exact matches
        const { chunks = [] } = await semanticSearchInDocumentChunks(projectId, searchQuery, 30)

        // Filter for exact phrase matches and high similarity
        const exactMatches = (chunks as ChunkResult[]).filter((chunk) => {
          const content = chunk.content.toLowerCase()
          const searchPhrase = phrase.toLowerCase()
          return content.includes(searchPhrase) && (chunk.similarity || 0) > 0.3
        })

        if (exactMatches.length === 0) {
          return `Aucune correspondance exacte trouvée pour "${phrase}". Essayez une recherche plus générale ou vérifiez l'orthographe.`
        }

        const results = exactMatches
          .slice(0, 15)
          .map((chunk, index) => {
            const content = chunk.content
            const fileName = chunk.originalFileName || "Document inconnu"
            const page = chunk.pageNumber ? ` (page ${chunk.pageNumber})` : ""

            // Highlight the exact phrase in context
            const highlightedContent = content.replace(
              new RegExp(phrase.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "gi"),
              `**${phrase}**`
            )

            return `${index + 1}. **${fileName}**${page}:\n${highlightedContent}\n`
          })
          .join("\n")

        return `Correspondances exactes trouvées pour "${phrase}":\n\n${results}`
      },
    })

    const frenchConstructionSearchTool = tool({
      description:
        "Recherche optimisée pour les documents de construction français. Utilise la terminologie technique française et comprend le contexte du bâtiment.",
      parameters: z.object({
        query: z.string().describe("Requête en français avec terminologie de construction"),
        searchType: z
          .enum(["exact", "semantic", "technical", "exploratory"])
          .optional()
          .default("semantic")
          .describe("Type de recherche à effectuer"),
        focus: z
          .enum(["materials", "techniques", "norms", "measurements", "general"])
          .optional()
          .default("general")
          .describe("Domaine de focus pour la recherche"),
      }),
      execute: async ({ query, searchType, focus }) => {
        logger.log("French construction search tool called", { query, searchType, focus, projectId })

        const frenchSearch = new FrenchConstructionSearch()

        // Enhance query based on focus area
        let enhancedQuery = query
        if (focus === "materials") {
          enhancedQuery += " matériaux béton acier bois isolation"
        } else if (focus === "techniques") {
          enhancedQuery += " technique méthode procédé mise en œuvre"
        } else if (focus === "norms") {
          enhancedQuery += " norme DTU réglementation RT2020 RE2020"
        } else if (focus === "measurements") {
          enhancedQuery += " dimension mesure quantité surface volume"
        }

        // Determine search strategies based on search type
        let strategies: ("exact" | "semantic" | "technical" | "contextual")[]
        switch (searchType) {
          case "exact":
            strategies = ["exact"]
            break
          case "technical":
            strategies = ["technical", "exact"]
            break
          case "exploratory":
            strategies = ["semantic", "contextual"]
            break
          default:
            strategies = ["exact", "semantic", "technical"]
        }

        const results = await frenchSearch.performFrenchConstructionSearch(projectId, enhancedQuery, {
          maxResults: 20,
          minSimilarity: searchType === "exact" ? 0.7 : 0.3,
          includeContext: true,
          searchStrategies: strategies,
          frenchOptimized: true,
          constructionFocus: true,
        })

        if (results.length === 0) {
          return `Aucun résultat trouvé pour "${query}". Essayez avec des termes plus généraux ou vérifiez la terminologie française.`
        }

        // Analyze results for insights
        const frenchTermsFound = new Set<string>()
        const constructionContexts = new Set<string>()

        results.forEach((result) => {
          result.frenchTermsMatched.forEach((term) => frenchTermsFound.add(term))
          if (result.constructionContext) {
            constructionContexts.add(result.constructionContext)
          }
        })

        const formattedResults = results
          .slice(0, 15)
          .map((result, index) => {
            const fileName = result.documentName || "Document inconnu"
            const page = result.pageNumber ? ` (page ${result.pageNumber})` : ""
            const section = result.section ? ` - ${result.section}` : ""
            const matchType =
              result.matchType === "exact_french"
                ? "🎯 Exact"
                : result.matchType === "technical_french"
                  ? "🔧 Technique"
                  : result.matchType === "semantic_french"
                    ? "🧠 Sémantique"
                    : "📝 Contextuel"

            const content =
              result.extractedPassages.length > 0
                ? result.extractedPassages.join(" ... ")
                : result.content.substring(0, 400)

            return (
              `${index + 1}. **${fileName}**${page}${section} ${matchType}\n` +
              `Similarité: ${(result.similarity * 100).toFixed(1)}% | Confiance: ${(result.confidence * 100).toFixed(1)}%\n` +
              `Termes français: ${result.frenchTermsMatched.join(", ") || "Aucun"}\n` +
              `Contexte: ${result.constructionContext}\n` +
              `${content}\n`
            )
          })
          .join("\n")

        const insights =
          `\n**📊 Analyse de la recherche:**\n` +
          `• ${results.length} résultats trouvés\n` +
          `• ${frenchTermsFound.size} termes français identifiés: ${Array.from(frenchTermsFound).slice(0, 5).join(", ")}\n` +
          `• Confiance moyenne: ${((results.reduce((sum, r) => sum + r.confidence, 0) / results.length) * 100).toFixed(1)}%\n` +
          `• Similarité moyenne: ${((results.reduce((sum, r) => sum + r.similarity, 0) / results.length) * 100).toFixed(1)}%`

        return `**🏗️ Recherche Construction Française:** "${query}"\n\n${formattedResults}\n${insights}`
      },
    })

    const result = await streamText({
      model: openai("gpt-4o"),
      system: systemPrompt,
      messages,
      tools: {
        deepSearchTool,
        projectAnalysisTool,
        documentSearchTool,
        documentIdentificationTool,
        exhaustiveDocumentSearchTool,
        specificInformationSearchTool,
        advancedPrecisionSearchTool,
        exactSearchTool,
        frenchConstructionSearchTool,
      },
      maxSteps: 6, // Increased for more thorough analysis
      temperature: 0.05, // Even more deterministic for precise retrieval
    })

    logger.log("Chat response generated", {
      projectId,
      messagesCount: messages.length,
      documentsAvailable: project.allProjectDocuments.length,
    })

    return result.toDataStreamResponse()
  } catch (error) {
    logger.error("Chat request failed", { error })
    return new Response(
      `Erreur lors du traitement de la requête: ${error instanceof Error ? error.message : "Erreur inconnue"}`,
      { status: 500 }
    )
  }
}
