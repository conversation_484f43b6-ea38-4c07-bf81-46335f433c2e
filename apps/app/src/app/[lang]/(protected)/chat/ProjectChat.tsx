"use client"

import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react"
import Image from "next/image"

import { ChatInput } from "@/components/chatcomponents/chatinput"
import { ChatMessage } from "@/components/chatcomponents/chatmessage"
import { ProjectInfoComponent } from "@/components/project/project-info"
import { trpcClient } from "@/lib/trpc/client"
import type { FolderType } from "@/types/index-type"
import { type Message, useChat } from "@ai-sdk/react"
import { logger } from "@buildismart/lib"

interface ProjectChatProps {
  project: FolderType
}

export default function ProjectChat({ project }: ProjectChatProps) {
  const [chat, setChat] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isChatEnabled, setIsChatEnabled] = useState(false)
  const [projectStatus, setProjectStatus] = useState<any>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Utiliser useChat avec un ID personnalisé basé sur le projet
  const {
    messages,
    input,
    handleInputChange,
    append,
    isLoading: isAILoading,
    setMessages,
  } = useChat({
    api: "/api/chat",
    id: `project-${project.id}`,
    body: {
      projectId: project.id,
    },
    onFinish: async (message) => {
      // Sauvegarder le message de l'assistant
      if (chat?.id) {
        await saveMessage(chat.id, "assistant", message.content)
      }
    },
  })

  // Charger ou créer le chat pour ce projet
  useEffect(() => {
    loadChat()
  }, [project.id])

  // Vérifier le statut du projet
  useEffect(() => {
    checkProjectStatus()
  }, [project.id])

  // Auto-scroll vers le bas
  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const loadChat = async () => {
    try {
      setIsLoading(true)
      const chatData = await trpcClient.chat.getOrCreateChat.query({
        projectId: project.id,
      })

      setChat(chatData)

      // Charger les messages existants dans useChat
      if (chatData.messages.length > 0) {
        const formattedMessages = chatData.messages.map((msg: any) => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          createdAt: msg.createdAt,
        }))
        setMessages(formattedMessages)
      }

      logger.log("Chat loaded:", chatData)
    } catch (error) {
      logger.error("Error loading chat:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const checkProjectStatus = async () => {
    try {
      const status = await trpcClient.folders.getProjectStatus.query({
        projectId: project.id,
      })
      setProjectStatus(status)

      // Activer le chat si il y a des documents prêts OU si c'est un projet sans documents (pour permettre les questions générales)
      const canChat = status.totalDocuments === 0 || status.readyDocuments > 0
      setIsChatEnabled(canChat)

      logger.log("Project status:", status)
      logger.log("Chat enabled:", canChat)
    } catch (error) {
      logger.error("Error checking project status:", error)
      // En cas d'erreur, permettre le chat quand même
      setIsChatEnabled(true)
    }
  }

  const saveMessage = async (chatId: string, role: "user" | "assistant", content: string) => {
    try {
      await trpcClient.chat.saveMessage.mutate({
        chatId,
        role,
        content,
      })
      logger.log("Message saved:", { role, contentLength: content.length })
    } catch (error) {
      logger.error("Error saving message:", error)
    }
  }

  const handleSubmit = async (userInput: string) => {
    if (!isChatEnabled) {
      logger.warn("Chat is disabled - documents not ready")
      return
    }

    if (!chat?.id) {
      logger.warn("No chat available")
      return
    }

    if (userInput.trim()) {
      // Sauvegarder le message de l'utilisateur
      await saveMessage(chat.id, "user", userInput)

      // Vider l'input immédiatement
      handleInputChange({ target: { value: "" } } as React.ChangeEvent<HTMLTextAreaElement>)

      // Envoyer le message à l'IA
      await append({
        content: userInput,
        role: "user",
      })
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <div className="mb-4 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p>Chargement du chat...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen items-center justify-center bg-gray-100 px-2">
      <div className="flex size-full max-w-2xl flex-col">
        <div className="flex-1 overflow-y-auto px-4 py-2 pb-24 scrollbar-hide">
          {/* Afficher les informations du projet en haut, même sans messages */}
          {!isLoading && (
            <ProjectInfoComponent projectId={project.id} />
          )}

          {messages.length === 0 ? (
            <div className="flex h-full flex-1 flex-col items-center justify-center gap-4 text-center">
              <div className="space-y-2">
                <Image src={`/robot.png?${Date.now()}`} alt="Robot" width={50} height={50} />
                <h2 className="text-xl font-bold text-black">
                  Bienvenue sur votre assistant d&rsquo;analyse de DCE !
                </h2>
                {isChatEnabled ? (
                  <p className="text-sm text-gray-500">
                    Vous pouvez maintenant poser vos questions sur les documents analysés.
                  </p>
                ) : (
                  <div className="space-y-1">
                    <p className="text-sm text-yellow-600">
                      {projectStatus ? `${projectStatus.readyDocuments}/${projectStatus.totalDocuments} documents prêts` : "Vérification du statut..."}
                    </p>
                    {projectStatus?.totalDocuments === 0 ? (
                      <p className="text-xs text-gray-500">
                        Veuillez télécharger des documents pour commencer l'analyse.
                      </p>
                    ) : projectStatus?.readyDocuments === 0 ? (
                      <p className="text-xs text-gray-500">
                        Les documents sont en cours de traitement. Le chat sera activé une fois l'analyse terminée.
                      </p>
                    ) : (
                      <p className="text-xs text-gray-500">
                        Préparation du chat en cours...
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center gap-4 pb-4">
              {messages.map((message, index) => (
                <div key={index} className="flex w-full max-w-3xl flex-col gap-2">
                  <ChatMessage role={message.role as "user" | "assistant"} content={message.content} />
                </div>
              ))}
              {isAILoading && <ChatMessage role="assistant" content="⏳ Rédaction de la réponse..." />}
            </div>
          )}
        </div>

        <div className="sticky bottom-0 flex justify-center border-t bg-gray-100 py-2">
          <ChatInput
            input={input}
            onInputChange={handleInputChange}
            onSubmit={() => handleSubmit(input)}
            disabled={!isChatEnabled || isAILoading}
            placeholder={
              isChatEnabled
                ? "Posez votre question..."
                : projectStatus?.totalDocuments === 0
                  ? "Téléchargez des documents pour commencer..."
                  : "Traitement en cours..."
            }
          />
        </div>
      </div>
    </div>
  )
}
