"use client"

import React from "react"
import { CheckCircle, FileText, Upload, XCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@nextui-org/button"
import { Progress } from "@nextui-org/progress"

export type UploadStatus = "idle" | "uploading" | "processing" | "success" | "error"

export interface UploadItem {
  id: string
  name: string
  size: number
  status: UploadStatus
  progress: number
  error?: string
}

interface UploadProgressProps {
  items: UploadItem[]
  onRetry?: (id: string) => void
  onCancel?: (id: string) => void
  onClose?: () => void
  isVisible: boolean
}

export function UploadProgress({
  items,
  onRetry,
  onCancel,
  onClose,
  isVisible
}: UploadProgressProps) {
  if (!isVisible || items.length === 0) {
    return null
  }

  const formatFileSize = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const getStatusIcon = (status: UploadStatus) => {
    switch (status) {
      case "success":
        return <CheckCircle className="size-4 text-green-500" />
      case "error":
        return <XCircle className="size-4 text-red-500" />
      case "uploading":
      case "processing":
        return <Upload className="size-4 text-blue-500 animate-pulse" />
      default:
        return <FileText className="size-4 text-gray-400" />
    }
  }

  const getStatusText = (status: UploadStatus) => {
    switch (status) {
      case "uploading":
        return "Téléchargement..."
      case "processing":
        return "Traitement..."
      case "success":
        return "Terminé"
      case "error":
        return "Erreur"
      default:
        return "En attente"
    }
  }

  const getStatusColor = (status: UploadStatus) => {
    switch (status) {
      case "success":
        return "success"
      case "error":
        return "danger"
      case "uploading":
      case "processing":
        return "primary"
      default:
        return "default"
    }
  }

  const allCompleted = items.every(item => item.status === "success" || item.status === "error")
  const hasErrors = items.some(item => item.status === "error")
  const totalProgress = items.length > 0
    ? Math.round(items.reduce((sum, item) => sum + item.progress, 0) / items.length)
    : 0

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-w-[calc(100vw-2rem)] rounded-lg border border-gray-200 bg-white shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-100 p-4">
        <div className="flex items-center gap-2">
          <Upload className="size-5 text-blue-600" />
          <h3 className="font-medium text-gray-900">
            Téléchargement ({items.length} fichier{items.length > 1 ? 's' : ''})
          </h3>
        </div>
        {allCompleted && onClose && (
          <Button
            size="sm"
            variant="light"
            onPress={onClose}
            className="min-w-0 p-1"
          >
            <XCircle className="size-4" />
          </Button>
        )}
      </div>

      {/* Overall Progress */}
      {!allCompleted && (
        <div className="p-4 border-b border-gray-100">
          <div className="flex justify-between text-sm mb-2">
            <span className="text-gray-600">Progression globale</span>
            <span className="font-medium">{totalProgress}%</span>
          </div>
          <Progress
            value={totalProgress}
            color={hasErrors ? "danger" : "primary"}
            className="w-full"
          />
        </div>
      )}

      {/* File List */}
      <div className="max-h-64 overflow-y-auto">
        {items.map((item) => (
          <div key={item.id} className="border-b border-gray-50 p-3 last:border-b-0">
            <div className="flex items-center gap-3">
              {getStatusIcon(item.status)}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {item.name}
                  </p>
                  <span className="text-xs text-gray-500 ml-2">
                    {formatFileSize(item.size)}
                  </span>
                </div>

                <div className="flex items-center justify-between mt-1">
                  <span className="text-xs text-gray-500">
                    {getStatusText(item.status)}
                  </span>
                  {item.status !== "idle" && item.status !== "success" && item.status !== "error" && (
                    <span className="text-xs font-medium text-gray-700">
                      {item.progress}%
                    </span>
                  )}
                </div>

                {item.status === "error" && item.error && (
                  <p className="text-xs text-red-600 mt-1">{item.error}</p>
                )}

                {(item.status === "uploading" || item.status === "processing") && (
                  <Progress
                    value={item.progress}
                    color={getStatusColor(item.status)}
                    size="sm"
                    className="mt-2"
                  />
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-1">
                {item.status === "error" && onRetry && (
                  <Button
                    size="sm"
                    variant="light"
                    color="primary"
                    onPress={() => onRetry(item.id)}
                    className="min-w-0 px-2"
                  >
                    Réessayer
                  </Button>
                )}
                {(item.status === "uploading" || item.status === "processing") && onCancel && (
                  <Button
                    size="sm"
                    variant="light"
                    color="danger"
                    onPress={() => onCancel(item.id)}
                    className="min-w-0 px-2"
                  >
                    Annuler
                  </Button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      {allCompleted && (
        <div className="p-3 bg-gray-50 rounded-b-lg">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              {hasErrors
                ? `${items.filter(i => i.status === "error").length} erreur(s)`
                : "Tous les fichiers ont été traités"
              }
            </span>
            {onClose && (
              <Button
                size="sm"
                variant="light"
                onPress={onClose}
              >
                Fermer
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
