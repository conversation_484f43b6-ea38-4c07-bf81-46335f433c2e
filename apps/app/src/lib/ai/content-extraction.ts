import * as mammoth from "mammoth"
import { extract } from "node-tika"
import { OpenAI } from "openai"
import { pdfToText } from "pdf-ts"
import { createWorker } from "tesseract.js"

import { env } from "@/lib/env"
import { logger } from "@buildismart/lib"

const openai = new OpenAI({
  apiKey: env.OPENAI_API_KEY,
})

export interface ExtractedContent {
  extractedText: string
  summary: string
  metadata: {
    wordCount: number
    pageCount: number
    size: number
    lastModified: string
  }
}

export interface ProcessingError {
  message: string
  code: string
  details?: unknown
}

/**
 * Extract text from image using OCR (Tesseract.js) with memory management
 */
async function extractTextFromImage(buffer: Buffer): Promise<string> {
  // Check buffer size to prevent memory issues
  const MAX_IMAGE_SIZE = 50 * 1024 * 1024 // 50MB limit for images
  if (buffer.length > MAX_IMAGE_SIZE) {
    throw new Error(`Image too large: ${buffer.length} bytes exceeds ${MAX_IMAGE_SIZE} bytes limit`)
  }

  const worker = await createWorker({
    logger: (m) => logger.log(m),
  })

  try {
    await worker.load()
    await worker.loadLanguage("fra+eng")
    await worker.initialize("fra+eng")

    const { data } = await worker.recognize(buffer)

    // Clear buffer reference to help GC
    buffer = null as any

    return data.text
  } finally {
    await worker.terminate()
  }
}

/**
 * Extract text from various file types with enhanced memory management and streaming
 */
async function extractTextFromFile(file: File): Promise<string> {
  // Reduced file size limits to prevent memory issues
  const MAX_FILE_SIZE = 50 * 1024 * 1024 // Reduced to 50MB limit
  if (file.size > MAX_FILE_SIZE) {
    throw new Error(`File too large: ${file.size} bytes exceeds ${MAX_FILE_SIZE} bytes limit`)
  }

  let buffer: Buffer | null = null
  let extractedText: string = ""

  try {
    // Monitor memory before processing
    const startMemory = process.memoryUsage()
    logger.log("Starting text extraction", {
      fileName: file.name,
      fileSize: file.size,
      mimeType: file.type,
      startMemory: Math.round(startMemory.heapUsed / 1024 / 1024) + "MB",
    })

    // Process file in chunks for large files
    if (file.size > 10 * 1024 * 1024) {
      // 10MB threshold for chunked processing
      extractedText = await extractTextFromLargeFile(file)
    } else {
      buffer = Buffer.from(await file.arrayBuffer())
      extractedText = await extractTextFromBuffer(buffer, file.type, file.name)
    }

    const endMemory = process.memoryUsage()
    logger.log("Text extraction completed", {
      fileName: file.name,
      extractedLength: extractedText.length,
      memoryDelta: Math.round((endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024) + "MB",
    })

    return extractedText
  } catch (error) {
    logger.warn("Fallback to plain text extraction", { error, fileName: file.name })
    try {
      return await file.text()
    } catch (textError) {
      logger.error("Failed to extract text from file", {
        fileName: file.name,
        originalError: error,
        textError,
      })
      throw new Error(`Text extraction failed: ${error instanceof Error ? error.message : "Unknown error"}`)
    }
  } finally {
    // Aggressive cleanup
    buffer = null
    if (global.gc) {
      global.gc()
    }
  }
}

/**
 * Extract text from large files using chunked processing
 */
async function extractTextFromLargeFile(file: File): Promise<string> {
  const CHUNK_SIZE = 5 * 1024 * 1024 // 5MB chunks
  const chunks: string[] = []

  try {
    for (let offset = 0; offset < file.size; offset += CHUNK_SIZE) {
      const chunk = file.slice(offset, offset + CHUNK_SIZE)
      const chunkBuffer = Buffer.from(await chunk.arrayBuffer())

      try {
        const chunkText = await extractTextFromBuffer(chunkBuffer, file.type, file.name)
        chunks.push(chunkText)
      } catch (error) {
        logger.warn("Failed to extract chunk, skipping", { offset, error })
      }

      // Force cleanup after each chunk
      if (global.gc) {
        global.gc()
      }
    }

    return chunks.join("\n")
  } catch (error) {
    logger.error("Large file processing failed", { fileName: file.name, error })
    throw error
  }
}

/**
 * Extract text from buffer with type-specific processing
 */
async function extractTextFromBuffer(buffer: Buffer, mimeType: string, fileName: string): Promise<string> {
  try {
    if (mimeType === "application/pdf") {
      const result = await pdfToText(buffer)
      return result
    }

    if (mimeType.includes("word") || fileName.endsWith(".docx") || fileName.endsWith(".doc")) {
      const result = await mammoth.extractRawText({ buffer })
      return result.value
    }

    if (mimeType.startsWith("image/")) {
      return await extractTextFromImage(buffer)
    }

    // Fallback to node-tika for other formats
    return await extract({ buffer })
  } finally {
    // Clear buffer reference
    buffer = null as any
  }
}

/**
 * Generate construction-specific file summary using enhanced service
 */
async function generateConstructionSummary(content: string, fileName: string): Promise<string> {
  // Import the enhanced construction summary service
  const { generateConstructionFileSummary } = await import("@/lib/ai/construction-summary")

  try {
    const result = await generateConstructionFileSummary(content, fileName)
    return result.summary
  } catch (error) {
    logger.warn("Enhanced summary generation failed, falling back to basic summary", { fileName, error })

    // Fallback to basic summary generation
    const truncatedContent = content.substring(0, 15000)

    const chatResponse = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: `Tu es un expert en analyse de documents de construction (BTP - Bâtiment et Travaux Publics).
          Analyse ce document et fournis un résumé concis en 2-3 lignes maximum.
          Focus sur les éléments clés pour les appels d'offres:
          - Type de document (CCTP, DPGF, plans, etc.)
          - Éléments techniques principaux
          - Contraintes ou spécifications importantes
          - Quantités ou coûts si mentionnés

          Format: Maximum 200 caractères, style télégraphique, sans verbes superflus.`,
        },
        {
          role: "user",
          content: `Fichier: ${fileName}\n\nContenu:\n${truncatedContent}`,
        },
      ],
      temperature: 0.3,
      max_tokens: 150,
    })

    return chatResponse.choices[0]?.message?.content || "Résumé indisponible"
  }
}

/**
 * Main content extraction function
 */
export async function extractContentFromFile(file: File): Promise<ExtractedContent> {
  try {
    const extractedText = await extractTextFromFile(file)
    const summary = await generateConstructionSummary(extractedText, file.name)

    const metadata = {
      wordCount: extractedText.split(/\s+/).length,
      pageCount: extractedText.includes("\f") ? extractedText.split("\f").length : 1,
      size: file.size,
      lastModified: new Date(file.lastModified).toISOString(),
    }

    return {
      extractedText: `**${file.name}**:\n${extractedText}`,
      summary: summary.trim(),
      metadata,
    }
  } catch (error) {
    logger.error("Content extraction failed", { error, fileName: file.name })
    throw new Error(`Échec de l'extraction de contenu: ${error instanceof Error ? error.message : "Erreur inconnue"}`)
  }
}

/**
 * Extract content from buffer (for S3 files)
 */
export async function extractContentFromBuffer(
  buffer: Buffer,
  fileName: string,
  mimeType: string
): Promise<ExtractedContent> {
  try {
    // Create a File-like object from buffer
    const file = new File([buffer], fileName, { type: mimeType })
    return await extractContentFromFile(file)
  } catch (error) {
    logger.error("Buffer content extraction failed", { error, fileName })
    throw new Error(`Échec de l'extraction de contenu: ${error instanceof Error ? error.message : "Erreur inconnue"}`)
  }
}
