import { embed, embedMany } from "ai"
import { prisma } from "@/lib/prisma"
import { ChunkMetadata } from "@/types/project"
import { openai } from "@ai-sdk/openai"
import { logger } from "@buildismart/lib"

// Advanced search interfaces
export interface SemanticSearchQuery {
  originalQuery: string
  expandedQueries: string[]
  keywords: string[]
  context: string
  domain: "construction" | "technical" | "financial" | "planning" | "general"
}

export interface PreciseSearchResult {
  id: string
  content: string
  documentName: string
  pageNumber: number
  section?: string
  similarity: number
  matchType: "exact" | "semantic" | "contextual" | "keyword"
  relevanceScore: number
  sourceAttribution: {
    fileName: string
    page: number
    section?: string
    startPosition?: number
    endPosition?: number
  }
  extractedPassages: string[]
  contextBefore?: string
  contextAfter?: string
}

export interface AdvancedSearchOptions {
  maxResults: number
  minSimilarity: number
  includeContext: boolean
  searchStrategies: ("exact" | "semantic" | "contextual" | "keyword")[]
  domainFocus?: string
}

/**
 * Advanced semantic search with contextual understanding and precise attribution
 */
export class AdvancedSemanticSearch {
  private embeddingModel = openai.embedding("text-embedding-3-small")

  /**
   * Analyze and expand user query for comprehensive search
   */
  async analyzeQuery(query: string): Promise<SemanticSearchQuery> {
    const analysisPrompt = `Tu es un expert en analyse de requêtes pour la recherche documentaire dans le domaine de la construction/BTP.

Analyse cette requête utilisateur et génère des variantes sémantiques pour une recherche exhaustive:
"${query}"

Fournis une réponse JSON avec:
{
  "originalQuery": "${query}",
  "expandedQueries": ["variante1", "variante2", "variante3"],
  "keywords": ["mot-clé1", "mot-clé2"],
  "context": "contexte détaillé de la recherche",
  "domain": "construction|technical|financial|planning|general"
}

Exemples de variantes sémantiques:
- "config des couleurs" → ["configuration couleurs", "palette de couleurs", "paramètres chromatiques", "thème visuel"]
- "boilerplate" → ["modèle de base", "template", "structure initiale", "squelette de projet"]
- "prix béton" → ["coût béton", "tarif béton", "devis béton", "budget béton"]`

    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: analysisPrompt }],
        temperature: 0.3,
        max_tokens: 500,
      })

      const analysisResult = JSON.parse(response.choices[0]?.message?.content || "{}")

      return {
        originalQuery: query,
        expandedQueries: analysisResult.expandedQueries || [query],
        keywords: analysisResult.keywords || [query],
        context: analysisResult.context || "",
        domain: analysisResult.domain || "general",
      }
    } catch (error) {
      logger.error("Query analysis failed", { error, query })
      return {
        originalQuery: query,
        expandedQueries: [query],
        keywords: [query],
        context: "",
        domain: "general",
      }
    }
  }

  /**
   * Multi-pass search with exact, semantic, and contextual strategies
   */
  async performAdvancedSearch(
    projectId: string,
    query: string,
    options: AdvancedSearchOptions = {
      maxResults: 30,
      minSimilarity: 0.2,
      includeContext: true,
      searchStrategies: ["exact", "semantic", "contextual", "keyword"],
    }
  ): Promise<PreciseSearchResult[]> {
    const startTime = Date.now()
    logger.log("Starting advanced semantic search", { projectId, query, options })

    // Step 1: Analyze and expand query
    const analyzedQuery = await this.analyzeQuery(query)

    // Step 2: Perform multi-pass search
    const allResults: PreciseSearchResult[] = []

    // Pass 1: Exact phrase matching
    if (options.searchStrategies.includes("exact")) {
      const exactResults = await this.exactPhraseSearch(projectId, analyzedQuery)
      allResults.push(...exactResults)
    }

    // Pass 2: Semantic similarity search
    if (options.searchStrategies.includes("semantic")) {
      const semanticResults = await this.semanticSimilaritySearch(projectId, analyzedQuery)
      allResults.push(...semanticResults)
    }

    // Pass 3: Contextual expansion search
    if (options.searchStrategies.includes("contextual")) {
      const contextualResults = await this.contextualExpansionSearch(projectId, analyzedQuery)
      allResults.push(...contextualResults)
    }

    // Pass 4: Keyword-based search
    if (options.searchStrategies.includes("keyword")) {
      const keywordResults = await this.keywordSearch(projectId, analyzedQuery)
      allResults.push(...keywordResults)
    }

    // Step 3: Deduplicate and rank results
    const deduplicatedResults = this.deduplicateResults(allResults)
    const rankedResults = this.rankResults(deduplicatedResults, analyzedQuery)

    // Step 4: Filter and limit results
    const filteredResults = rankedResults
      .filter((result) => result.relevanceScore >= options.minSimilarity)
      .slice(0, options.maxResults)

    // Step 5: Enhance with context if requested
    if (options.includeContext) {
      await this.enhanceWithContext(filteredResults)
    }

    const processingTime = Date.now() - startTime
    logger.log("Advanced search completed", {
      projectId,
      query,
      analyzedQuery,
      totalResults: allResults.length,
      deduplicatedResults: deduplicatedResults.length,
      finalResults: filteredResults.length,
      processingTime,
    })

    return filteredResults
  }

  /**
   * Exact phrase matching with high precision
   */
  private async exactPhraseSearch(
    projectId: string,
    analyzedQuery: SemanticSearchQuery
  ): Promise<PreciseSearchResult[]> {
    const results: PreciseSearchResult[] = []

    // Search for original query and expanded queries
    const searchTerms = [analyzedQuery.originalQuery, ...analyzedQuery.expandedQueries]

    for (const term of searchTerms) {
      const chunks = await prisma.$queryRaw<any[]>`
        SELECT
          dc.id,
          dc.content,
          dc."pageNumber",
          dc.metadata,
          dc."projectDocumentId" as "documentId",
          pd."originalFileName",
          pd."mimeType",
          f.name as "folderName"
        FROM "DocumentChunk" dc
        JOIN "ProjectDocument" pd ON dc."projectDocumentId" = pd.id
        LEFT JOIN "Folder" f ON pd."folderId" = f.id
        WHERE pd."projectId" = ${projectId}
          AND pd.status = 'READY'
          AND LOWER(dc.content) LIKE LOWER(${`%${term}%`})
        ORDER BY LENGTH(dc.content) ASC
        LIMIT 20
      `

      for (const chunk of chunks) {
        const passages = this.extractRelevantPassages(chunk.content, term)

        results.push({
          id: chunk.id,
          content: chunk.content,
          documentName: chunk.originalFileName,
          pageNumber: chunk.pageNumber || 1,
          section: (chunk.metadata as ChunkMetadata)?.section,
          similarity: 1.0, // Exact match gets highest similarity
          matchType: "exact",
          relevanceScore: 1.0,
          sourceAttribution: {
            fileName: chunk.originalFileName,
            page: chunk.pageNumber || 1,
            section: (chunk.metadata as ChunkMetadata)?.section,
          },
          extractedPassages: passages,
        })
      }
    }

    return results
  }

  /**
   * Extract relevant text passages around found terms
   */
  private extractRelevantPassages(content: string, searchTerm: string): string[] {
    const passages: string[] = []
    const contentLower = content.toLowerCase()
    const termLower = searchTerm.toLowerCase()

    let index = 0
    while ((index = contentLower.indexOf(termLower, index)) !== -1) {
      const start = Math.max(0, index - 100)
      const end = Math.min(content.length, index + searchTerm.length + 100)
      const passage = content.substring(start, end)

      // Highlight the found term
      const highlightedPassage = passage.replace(
        new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "gi"),
        `**${searchTerm}**`
      )

      passages.push(highlightedPassage)
      index += searchTerm.length
    }

    return passages
  }

  /**
   * Semantic similarity search using embeddings
   */
  private async semanticSimilaritySearch(
    projectId: string,
    analyzedQuery: SemanticSearchQuery
  ): Promise<PreciseSearchResult[]> {
    const results: PreciseSearchResult[] = []

    // Generate embeddings for all query variants
    const queryTexts = [analyzedQuery.originalQuery, ...analyzedQuery.expandedQueries]
    const embeddings = await embedMany({
      model: this.embeddingModel,
      values: queryTexts,
    })

    // Search with each embedding
    for (let i = 0; i < embeddings.embeddings.length; i++) {
      const embedding = embeddings.embeddings[i]
      const queryText = queryTexts[i]

      const chunks = await prisma.$queryRaw<any[]>`
        SELECT
          dc.id,
          dc.content,
          dc."pageNumber",
          dc.metadata,
          dc."projectDocumentId" as "documentId",
          pd."originalFileName",
          pd."mimeType",
          f.name as "folderName",
          1 - (dc.embedding <=> ${embedding}::vector) as similarity
        FROM "DocumentChunk" dc
        JOIN "ProjectDocument" pd ON dc."projectDocumentId" = pd.id
        LEFT JOIN "Folder" f ON pd."folderId" = f.id
        WHERE pd."projectId" = ${projectId}
          AND pd.status = 'READY'
          AND dc.embedding IS NOT NULL
        ORDER BY similarity DESC
        LIMIT 15
      `

      for (const chunk of chunks) {
        if (chunk.similarity < 0.3) continue // Skip low similarity results

        results.push({
          id: chunk.id,
          content: chunk.content,
          documentName: chunk.originalFileName,
          pageNumber: chunk.pageNumber || 1,
          section: (chunk.metadata as ChunkMetadata)?.section,
          similarity: chunk.similarity,
          matchType: "semantic",
          relevanceScore: chunk.similarity * 0.9, // Slightly lower than exact matches
          sourceAttribution: {
            fileName: chunk.originalFileName,
            page: chunk.pageNumber || 1,
            section: (chunk.metadata as ChunkMetadata)?.section,
          },
          extractedPassages: [chunk.content],
        })
      }
    }

    return results
  }

  /**
   * Contextual expansion search using domain knowledge
   */
  private async contextualExpansionSearch(
    projectId: string,
    analyzedQuery: SemanticSearchQuery
  ): Promise<PreciseSearchResult[]> {
    const results: PreciseSearchResult[] = []

    // Generate domain-specific expansions
    const contextualTerms = await this.generateContextualTerms(analyzedQuery)

    for (const term of contextualTerms) {
      const chunks = await prisma.$queryRaw<any[]>`
        SELECT
          dc.id,
          dc.content,
          dc."pageNumber",
          dc.metadata,
          dc."projectDocumentId" as "documentId",
          pd."originalFileName",
          pd."mimeType",
          f.name as "folderName"
        FROM "DocumentChunk" dc
        JOIN "ProjectDocument" pd ON dc."projectDocumentId" = pd.id
        LEFT JOIN "Folder" f ON pd."folderId" = f.id
        WHERE pd."projectId" = ${projectId}
          AND pd.status = 'READY'
          AND (
            LOWER(dc.content) LIKE LOWER(${`%${term}%`})
            OR dc.content ~* ${this.createRegexPattern(term)}
          )
        LIMIT 10
      `

      for (const chunk of chunks) {
        results.push({
          id: chunk.id,
          content: chunk.content,
          documentName: chunk.originalFileName,
          pageNumber: chunk.pageNumber || 1,
          section: (chunk.metadata as ChunkMetadata)?.section,
          similarity: 0.7, // Contextual matches get medium similarity
          matchType: "contextual",
          relevanceScore: 0.7,
          sourceAttribution: {
            fileName: chunk.originalFileName,
            page: chunk.pageNumber || 1,
            section: (chunk.metadata as ChunkMetadata)?.section,
          },
          extractedPassages: this.extractRelevantPassages(chunk.content, term),
        })
      }
    }

    return results
  }

  /**
   * Generate contextual terms based on domain knowledge
   */
  private async generateContextualTerms(query: SemanticSearchQuery): Promise<string[]> {
    const domainMappings = {
      construction: {
        couleur: ["palette", "thème", "chromatique", "teinte", "coloris"],
        config: ["configuration", "paramétrage", "réglage", "setup"],
        boilerplate: ["modèle", "template", "squelette", "structure de base"],
        prix: ["coût", "tarif", "devis", "budget", "montant"],
        béton: ["concrete", "mortier", "ciment", "granulat"],
      },
      technical: {
        interface: ["UI", "IHM", "écran", "affichage"],
        système: ["architecture", "infrastructure", "plateforme"],
        données: ["data", "informations", "base de données"],
      },
    }

    const contextualTerms: string[] = []
    const mapping = domainMappings[query.domain as keyof typeof domainMappings] || {}

    for (const keyword of query.keywords) {
      const keywordLower = keyword.toLowerCase()
      for (const [key, synonyms] of Object.entries(mapping)) {
        if (keywordLower.includes(key)) {
          contextualTerms.push(...synonyms)
        }
      }
    }

    return [...new Set(contextualTerms)] // Remove duplicates
  }

  /**
   * Create regex pattern for flexible matching
   */
  private createRegexPattern(term: string): string {
    // Create a flexible regex that allows for variations
    const escaped = term.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")
    return `\\b${escaped}\\w*\\b`
  }

  /**
   * Keyword-based search with fuzzy matching
   */
  private async keywordSearch(projectId: string, analyzedQuery: SemanticSearchQuery): Promise<PreciseSearchResult[]> {
    const results: PreciseSearchResult[] = []

    for (const keyword of analyzedQuery.keywords) {
      const chunks = await prisma.$queryRaw<any[]>`
        SELECT
          dc.id,
          dc.content,
          dc."pageNumber",
          dc.metadata,
          dc."projectDocumentId" as "documentId",
          pd."originalFileName",
          pd."mimeType",
          f.name as "folderName",
          ts_rank(to_tsvector('french', dc.content), plainto_tsquery('french', ${keyword})) as rank
        FROM "DocumentChunk" dc
        JOIN "ProjectDocument" pd ON dc."projectDocumentId" = pd.id
        LEFT JOIN "Folder" f ON pd."folderId" = f.id
        WHERE pd."projectId" = ${projectId}
          AND pd.status = 'READY'
          AND to_tsvector('french', dc.content) @@ plainto_tsquery('french', ${keyword})
        ORDER BY rank DESC
        LIMIT 10
      `

      for (const chunk of chunks) {
        results.push({
          id: chunk.id,
          content: chunk.content,
          documentName: chunk.originalFileName,
          pageNumber: chunk.pageNumber || 1,
          section: (chunk.metadata as ChunkMetadata)?.section,
          similarity: Math.min(chunk.rank * 2, 1.0), // Convert rank to similarity
          matchType: "keyword",
          relevanceScore: Math.min(chunk.rank * 2, 1.0) * 0.8,
          sourceAttribution: {
            fileName: chunk.originalFileName,
            page: chunk.pageNumber || 1,
            section: (chunk.metadata as ChunkMetadata)?.section,
          },
          extractedPassages: this.extractRelevantPassages(chunk.content, keyword),
        })
      }
    }

    return results
  }

  /**
   * Deduplicate results based on content similarity
   */
  private deduplicateResults(results: PreciseSearchResult[]): PreciseSearchResult[] {
    const seen = new Set<string>()
    const deduplicated: PreciseSearchResult[] = []

    for (const result of results) {
      const key = `${result.documentName}-${result.pageNumber}-${result.content.substring(0, 100)}`
      if (!seen.has(key)) {
        seen.add(key)
        deduplicated.push(result)
      }
    }

    return deduplicated
  }

  /**
   * Rank results based on multiple factors
   */
  private rankResults(results: PreciseSearchResult[], query: SemanticSearchQuery): PreciseSearchResult[] {
    return results
      .map((result) => {
        let score = result.relevanceScore

        // Boost exact matches
        if (result.matchType === "exact") score += 0.3

        // Boost results with multiple keyword matches
        const keywordMatches = query.keywords.filter((keyword) =>
          result.content.toLowerCase().includes(keyword.toLowerCase())
        ).length
        score += (keywordMatches / query.keywords.length) * 0.2

        // Boost results from specific document types
        if (result.documentName.toLowerCase().includes("cctp")) score += 0.1
        if (result.documentName.toLowerCase().includes("dpgf")) score += 0.1

        // Boost results with page numbers (more structured documents)
        if (result.pageNumber > 1) score += 0.05

        return { ...result, relevanceScore: Math.min(score, 1.0) }
      })
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
  }

  /**
   * Enhance results with surrounding context
   */
  private async enhanceWithContext(results: PreciseSearchResult[]): Promise<void> {
    for (const result of results) {
      try {
        // Get surrounding chunks for context
        const surroundingChunks = await prisma.$queryRaw<any[]>`
          SELECT content, "pageNumber", metadata
          FROM "DocumentChunk"
          WHERE "projectDocumentId" = ${result.sourceAttribution.fileName}
            AND "pageNumber" BETWEEN ${Math.max(1, result.pageNumber - 1)} AND ${result.pageNumber + 1}
          ORDER BY "pageNumber", id
        `

        if (surroundingChunks.length > 1) {
          const currentIndex = surroundingChunks.findIndex((chunk) => chunk.content === result.content)

          if (currentIndex > 0) {
            result.contextBefore = surroundingChunks[currentIndex - 1].content
          }

          if (currentIndex < surroundingChunks.length - 1) {
            result.contextAfter = surroundingChunks[currentIndex + 1].content
          }
        }
      } catch (error) {
        logger.warn("Failed to enhance context for result", { error, resultId: result.id })
      }
    }
  }
}
