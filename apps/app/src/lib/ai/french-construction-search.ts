import { embed, embed<PERSON>any } from "ai"
import { prisma } from "@/lib/prisma"
import { ChunkMetadata } from "@/types/project"
import { openai } from "@ai-sdk/openai"
import { logger } from "@buildismart/lib"

// Enhanced search interfaces for French construction documents
export interface FrenchConstructionSearchQuery {
  originalQuery: string
  normalizedQuery: string
  frenchTerms: string[]
  constructionKeywords: string[]
  technicalContext: string
  searchIntent: "exact" | "semantic" | "exploratory" | "technical"
  confidence: number
}

export interface FrenchSearchResult {
  id: string
  content: string
  documentName: string
  pageNumber: number
  section?: string
  similarity: number
  matchType: "exact_french" | "semantic_french" | "technical_french" | "contextual_french"
  relevanceScore: number
  frenchTermsMatched: string[]
  constructionContext: string
  sourceAttribution: {
    fileName: string
    page: number
    section?: string
    blockType?: string
  }
  extractedPassages: string[]
  confidence: number
}

export interface FrenchSearchOptions {
  maxResults: number
  minSimilarity: number
  includeContext: boolean
  searchStrategies: ("exact" | "semantic" | "technical" | "contextual")[]
  frenchOptimized: boolean
  constructionFocus: boolean
}

// French construction terminology dictionary
const FRENCH_CONSTRUCTION_DICTIONARY = {
  materials: [
    "béton",
    "armé",
    "acier",
    "bois",
    "pierre",
    "brique",
    "parpaing",
    "agglo",
    "mortier",
    "ciment",
    "chaux",
    "plâtre",
    "enduit",
    "crépi",
    "peinture",
    "isolation",
    "laine",
    "polystyrène",
    "polyuréthane",
    "fibre",
    "verre",
  ],
  structures: [
    "fondation",
    "semelle",
    "radier",
    "mur",
    "cloison",
    "poutre",
    "poteau",
    "dalle",
    "plancher",
    "charpente",
    "ossature",
    "ferme",
    "panne",
    "chevron",
    "couverture",
    "toiture",
    "faîtage",
    "égout",
    "rive",
    "noue",
  ],
  techniques: [
    "coffrage",
    "ferraillage",
    "coulage",
    "malaxage",
    "gâchage",
    "vibration",
    "cure",
    "décoffrage",
    "maçonnerie",
    "hourdage",
    "jointoiement",
    "ragréage",
    "étanchéité",
    "imperméabilisation",
    "drainage",
    "ventilation",
  ],
  equipment: [
    "grue",
    "bétonnière",
    "pompe",
    "vibreur",
    "compacteur",
    "niveleuse",
    "bulldozer",
    "pelle",
    "chargeuse",
    "camion",
    "benne",
    "échafaudage",
  ],
  documents: [
    "cctp",
    "dpgf",
    "dtu",
    "norme",
    "réglementation",
    "rt2012",
    "rt2020",
    "re2020",
    "permis",
    "construire",
    "déclaration",
    "travaux",
    "dossier",
    "technique",
  ],
  measurements: [
    "mètre",
    "centimètre",
    "millimètre",
    "kilomètre",
    "carré",
    "cube",
    "linéaire",
    "surface",
    "volume",
    "hauteur",
    "largeur",
    "longueur",
    "épaisseur",
    "diamètre",
  ],
}

// French language patterns for construction documents
const FRENCH_LANGUAGE_PATTERNS = {
  articles: ["le", "la", "les", "un", "une", "des", "du", "de", "d'"],
  prepositions: ["dans", "sur", "sous", "avec", "sans", "pour", "par", "vers", "chez"],
  conjunctions: ["et", "ou", "mais", "donc", "car", "ni", "or"],
  pronouns: ["il", "elle", "ils", "elles", "ce", "cette", "ces", "celui", "celle"],
  verbs: ["être", "avoir", "faire", "aller", "voir", "savoir", "pouvoir", "vouloir"],
}

/**
 * Enhanced search system for French construction documents
 */
export class FrenchConstructionSearch {
  private embeddingModel = openai.embedding("text-embedding-3-large")

  /**
   * Analyze and enhance French construction query
   */
  async analyzeQuery(query: string): Promise<FrenchConstructionSearchQuery> {
    const queryLower = query.toLowerCase()

    // Detect French construction terms
    const frenchTerms = this.extractFrenchTerms(queryLower)
    const constructionKeywords = this.extractConstructionKeywords(queryLower)

    // Normalize query for better matching
    const normalizedQuery = this.normalizeQuery(query)

    // Determine search intent
    const searchIntent = this.determineSearchIntent(queryLower, frenchTerms, constructionKeywords)

    // Calculate confidence based on French and construction term density
    const confidence = this.calculateQueryConfidence(queryLower, frenchTerms, constructionKeywords)

    // Generate technical context
    const technicalContext = this.generateTechnicalContext(frenchTerms, constructionKeywords)

    return {
      originalQuery: query,
      normalizedQuery,
      frenchTerms,
      constructionKeywords,
      technicalContext,
      searchIntent,
      confidence,
    }
  }

  /**
   * Perform comprehensive French construction search
   */
  async performFrenchConstructionSearch(
    projectId: string,
    query: string,
    options: FrenchSearchOptions
  ): Promise<FrenchSearchResult[]> {
    const startTime = Date.now()
    logger.log("Starting French construction search", { projectId, query, options })

    // Analyze query
    const analyzedQuery = await this.analyzeQuery(query)

    const allResults: FrenchSearchResult[] = []

    // Pass 1: Exact French phrase matching
    if (options.searchStrategies.includes("exact")) {
      const exactResults = await this.exactFrenchPhraseSearch(projectId, analyzedQuery)
      allResults.push(...exactResults)
    }

    // Pass 2: Semantic search with French optimization
    if (options.searchStrategies.includes("semantic")) {
      const semanticResults = await this.semanticFrenchSearch(projectId, analyzedQuery)
      allResults.push(...semanticResults)
    }

    // Pass 3: Technical construction term search
    if (options.searchStrategies.includes("technical")) {
      const technicalResults = await this.technicalConstructionSearch(projectId, analyzedQuery)
      allResults.push(...technicalResults)
    }

    // Pass 4: Contextual French search
    if (options.searchStrategies.includes("contextual")) {
      const contextualResults = await this.contextualFrenchSearch(projectId, analyzedQuery)
      allResults.push(...contextualResults)
    }

    // Deduplicate and rank results
    const deduplicatedResults = this.deduplicateResults(allResults)
    const rankedResults = this.rankFrenchResults(deduplicatedResults, analyzedQuery)

    // Filter by minimum similarity and limit results
    const filteredResults = rankedResults
      .filter((result) => result.similarity >= options.minSimilarity)
      .slice(0, options.maxResults)

    const processingTime = Date.now() - startTime
    logger.log("French construction search completed", {
      projectId,
      query,
      totalResults: allResults.length,
      deduplicatedResults: deduplicatedResults.length,
      finalResults: filteredResults.length,
      processingTime,
      queryAnalysis: {
        frenchTerms: analyzedQuery.frenchTerms.length,
        constructionKeywords: analyzedQuery.constructionKeywords.length,
        searchIntent: analyzedQuery.searchIntent,
        confidence: analyzedQuery.confidence,
      },
    })

    return filteredResults
  }

  /**
   * Extract French terms from query
   */
  private extractFrenchTerms(query: string): string[] {
    const terms: string[] = []

    // Check all French construction categories
    Object.values(FRENCH_CONSTRUCTION_DICTIONARY).forEach((category) => {
      category.forEach((term) => {
        if (query.includes(term)) {
          terms.push(term)
        }
      })
    })

    // Check French language patterns
    Object.values(FRENCH_LANGUAGE_PATTERNS).forEach((patterns) => {
      patterns.forEach((pattern) => {
        if (query.includes(` ${pattern} `) || query.startsWith(`${pattern} `) || query.endsWith(` ${pattern}`)) {
          terms.push(pattern)
        }
      })
    })

    return [...new Set(terms)] // Remove duplicates
  }

  /**
   * Extract construction-specific keywords
   */
  private extractConstructionKeywords(query: string): string[] {
    const keywords: string[] = []

    // Extract from construction dictionary
    Object.entries(FRENCH_CONSTRUCTION_DICTIONARY).forEach(([category, terms]) => {
      terms.forEach((term) => {
        if (query.includes(term)) {
          keywords.push(`${category}:${term}`)
        }
      })
    })

    return keywords
  }

  /**
   * Normalize French query for better matching
   */
  private normalizeQuery(query: string): string {
    return query
      .toLowerCase()
      .replace(/[àáâãäå]/g, "a")
      .replace(/[èéêë]/g, "e")
      .replace(/[ìíîï]/g, "i")
      .replace(/[òóôõö]/g, "o")
      .replace(/[ùúûü]/g, "u")
      .replace(/[ýÿ]/g, "y")
      .replace(/[ç]/g, "c")
      .replace(/[ñ]/g, "n")
      .trim()
  }

  /**
   * Determine search intent based on query analysis
   */
  private determineSearchIntent(
    query: string,
    frenchTerms: string[],
    constructionKeywords: string[]
  ): "exact" | "semantic" | "exploratory" | "technical" {
    // Exact intent: specific phrases in quotes or very specific terms
    if (query.includes('"') || query.includes("'")) {
      return "exact"
    }

    // Technical intent: high density of construction terms
    if (constructionKeywords.length >= 3) {
      return "technical"
    }

    // Semantic intent: moderate French and construction terms
    if (frenchTerms.length >= 2 && constructionKeywords.length >= 1) {
      return "semantic"
    }

    // Exploratory intent: general or vague queries
    return "exploratory"
  }

  /**
   * Calculate query confidence based on French and construction content
   */
  private calculateQueryConfidence(query: string, frenchTerms: string[], constructionKeywords: string[]): number {
    const words = query.split(/\s+/).length
    const frenchDensity = frenchTerms.length / words
    const constructionDensity = constructionKeywords.length / words

    return Math.min(frenchDensity * 0.6 + constructionDensity * 0.4, 1.0)
  }

  /**
   * Generate technical context for enhanced search
   */
  private generateTechnicalContext(frenchTerms: string[], constructionKeywords: string[]): string {
    const contexts: string[] = []

    // Add French construction context
    if (frenchTerms.length > 0) {
      contexts.push(`Termes français: ${frenchTerms.slice(0, 5).join(", ")}`)
    }

    // Add construction domain context
    if (constructionKeywords.length > 0) {
      contexts.push(`Contexte construction: ${constructionKeywords.slice(0, 3).join(", ")}`)
    }

    return contexts.join(" | ")
  }

  /**
   * Exact French phrase search with construction context
   */
  private async exactFrenchPhraseSearch(
    projectId: string,
    analyzedQuery: FrenchConstructionSearchQuery
  ): Promise<FrenchSearchResult[]> {
    const results: FrenchSearchResult[] = []

    // Search for exact phrases in French construction chunks
    const chunks = await prisma.$queryRaw<any[]>`
      SELECT
        dc.id,
        dc.content,
        dc."pageNumber",
        dc.metadata,
        dc."projectDocumentId" as "documentId",
        pd."originalFileName",
        pd."mimeType",
        f.name as "folderName"
      FROM "DocumentChunk" dc
      JOIN "ProjectDocument" pd ON dc."projectDocumentId" = pd.id
      LEFT JOIN "Folder" f ON pd."folderId" = f.id
      WHERE pd."projectId" = ${projectId}
        AND pd.status = 'READY'
        AND (
          LOWER(dc.content) LIKE ${`%${analyzedQuery.normalizedQuery}%`}
          OR ${
            analyzedQuery.frenchTerms.length > 0
              ? analyzedQuery.frenchTerms.map((term) => `LOWER(dc.content) LIKE '%${term}%'`).join(" OR ")
              : "FALSE"
          }
        )
      ORDER BY
        CASE
          WHEN LOWER(dc.content) LIKE ${`%${analyzedQuery.normalizedQuery}%`} THEN 1
          ELSE 2
        END,
        LENGTH(dc.content) ASC
      LIMIT 15
    `

    for (const chunk of chunks) {
      const content = chunk.content.toLowerCase()
      const metadata = chunk.metadata as ChunkMetadata

      // Calculate exact match score
      let similarity = 0
      let frenchTermsMatched: string[] = []

      // Exact phrase match
      if (content.includes(analyzedQuery.normalizedQuery)) {
        similarity = 1.0
      }

      // French terms matching
      for (const term of analyzedQuery.frenchTerms) {
        if (content.includes(term)) {
          frenchTermsMatched.push(term)
          similarity += 0.1
        }
      }

      // Boost for French construction chunks
      if (metadata?.language === "fr" && metadata?.domain === "construction") {
        similarity += 0.2
      }

      similarity = Math.min(similarity, 1.0)

      if (similarity > 0.3) {
        results.push({
          id: chunk.id,
          content: chunk.content,
          documentName: chunk.originalFileName,
          pageNumber: chunk.pageNumber || 1,
          section: metadata?.section,
          similarity,
          matchType: "exact_french",
          relevanceScore: similarity * 0.9, // High relevance for exact matches
          frenchTermsMatched,
          constructionContext: this.extractConstructionContext(chunk.content),
          sourceAttribution: {
            fileName: chunk.originalFileName,
            page: chunk.pageNumber || 1,
            section: metadata?.section,
            blockType: metadata?.blockType,
          },
          extractedPassages: this.extractRelevantPassages(chunk.content, analyzedQuery.originalQuery),
          confidence: analyzedQuery.confidence * similarity,
        })
      }
    }

    return results
  }

  /**
   * Semantic search optimized for French construction documents
   */
  private async semanticFrenchSearch(
    projectId: string,
    analyzedQuery: FrenchConstructionSearchQuery
  ): Promise<FrenchSearchResult[]> {
    const results: FrenchSearchResult[] = []

    // Generate enhanced query with French construction context
    const enhancedQuery = `${analyzedQuery.originalQuery} ${analyzedQuery.technicalContext}`
    const queryEmbedding = await embed({
      model: this.embeddingModel,
      value: enhancedQuery,
    })

    const chunks = await prisma.$queryRaw<any[]>`
      SELECT
        dc.id,
        dc.content,
        dc."pageNumber",
        dc.metadata,
        dc."projectDocumentId" as "documentId",
        pd."originalFileName",
        pd."mimeType",
        f.name as "folderName",
        1 - (dc.embedding <=> ${queryEmbedding.embedding}::vector) as similarity
      FROM "DocumentChunk" dc
      JOIN "ProjectDocument" pd ON dc."projectDocumentId" = pd.id
      LEFT JOIN "Folder" f ON pd."folderId" = f.id
      WHERE pd."projectId" = ${projectId}
        AND pd.status = 'READY'
        AND dc.embedding IS NOT NULL
      ORDER BY similarity DESC
      LIMIT 20
    `

    for (const chunk of chunks) {
      const metadata = chunk.metadata as ChunkMetadata
      let similarity = chunk.similarity || 0

      // Boost for French construction content
      if (metadata?.language === "fr" && metadata?.domain === "construction") {
        similarity += 0.15
      }

      // Boost for technical blocks
      if (metadata?.strategy === "french_technical") {
        similarity += 0.1
      }

      // French terms matching boost
      const frenchTermsMatched = analyzedQuery.frenchTerms.filter((term) => chunk.content.toLowerCase().includes(term))
      similarity += frenchTermsMatched.length * 0.05

      similarity = Math.min(similarity, 1.0)

      if (similarity > 0.2) {
        results.push({
          id: chunk.id,
          content: chunk.content,
          documentName: chunk.originalFileName,
          pageNumber: chunk.pageNumber || 1,
          section: metadata?.section,
          similarity,
          matchType: "semantic_french",
          relevanceScore: similarity * 0.8, // Good relevance for semantic matches
          frenchTermsMatched,
          constructionContext: this.extractConstructionContext(chunk.content),
          sourceAttribution: {
            fileName: chunk.originalFileName,
            page: chunk.pageNumber || 1,
            section: metadata?.section,
            blockType: metadata?.blockType,
          },
          extractedPassages: this.extractRelevantPassages(chunk.content, analyzedQuery.originalQuery),
          confidence: analyzedQuery.confidence * similarity,
        })
      }
    }

    return results
  }

  /**
   * Technical construction term search
   */
  private async technicalConstructionSearch(
    projectId: string,
    analyzedQuery: FrenchConstructionSearchQuery
  ): Promise<FrenchSearchResult[]> {
    const results: FrenchSearchResult[] = []

    // Search specifically in technical blocks
    const chunks = await prisma.$queryRaw<any[]>`
      SELECT
        dc.id,
        dc.content,
        dc."pageNumber",
        dc.metadata,
        dc."projectDocumentId" as "documentId",
        pd."originalFileName",
        pd."mimeType",
        f.name as "folderName"
      FROM "DocumentChunk" dc
      JOIN "ProjectDocument" pd ON dc."projectDocumentId" = pd.id
      LEFT JOIN "Folder" f ON pd."folderId" = f.id
      WHERE pd."projectId" = ${projectId}
        AND pd.status = 'READY'
        AND (
          dc.metadata->>'strategy' = 'french_technical'
          OR dc.metadata->>'domain' = 'construction'
        )
      ORDER BY
        CASE
          WHEN dc.metadata->>'strategy' = 'french_technical' THEN 1
          ELSE 2
        END
      LIMIT 15
    `

    for (const chunk of chunks) {
      const metadata = chunk.metadata as ChunkMetadata
      const content = chunk.content.toLowerCase()

      // Calculate technical relevance
      let similarity = 0.5 // Base score for technical chunks
      let frenchTermsMatched: string[] = []

      // Construction keywords matching
      for (const keyword of analyzedQuery.constructionKeywords) {
        const term = keyword.split(":")[1] || keyword
        if (content.includes(term)) {
          similarity += 0.15
          frenchTermsMatched.push(term)
        }
      }

      // French terms matching
      for (const term of analyzedQuery.frenchTerms) {
        if (content.includes(term)) {
          frenchTermsMatched.push(term)
          similarity += 0.1
        }
      }

      similarity = Math.min(similarity, 1.0)

      if (similarity > 0.4) {
        results.push({
          id: chunk.id,
          content: chunk.content,
          documentName: chunk.originalFileName,
          pageNumber: chunk.pageNumber || 1,
          section: metadata?.section,
          similarity,
          matchType: "technical_french",
          relevanceScore: similarity * 0.85,
          frenchTermsMatched,
          constructionContext: this.extractConstructionContext(chunk.content),
          sourceAttribution: {
            fileName: chunk.originalFileName,
            page: chunk.pageNumber || 1,
            section: metadata?.section,
            blockType: metadata?.blockType,
          },
          extractedPassages: this.extractRelevantPassages(chunk.content, analyzedQuery.originalQuery),
          confidence: analyzedQuery.confidence * similarity,
        })
      }
    }

    return results
  }

  /**
   * Contextual French search using PostgreSQL full-text search
   */
  private async contextualFrenchSearch(
    projectId: string,
    analyzedQuery: FrenchConstructionSearchQuery
  ): Promise<FrenchSearchResult[]> {
    const results: FrenchSearchResult[] = []

    // Use PostgreSQL French full-text search
    const searchTerms = [
      ...analyzedQuery.frenchTerms,
      ...analyzedQuery.constructionKeywords.map((k) => k.split(":")[1] || k),
    ]
      .filter((term) => term.length > 2)
      .slice(0, 5) // Limit to avoid query complexity

    if (searchTerms.length === 0) return results

    const chunks = await prisma.$queryRaw<any[]>`
      SELECT
        dc.id,
        dc.content,
        dc."pageNumber",
        dc.metadata,
        dc."projectDocumentId" as "documentId",
        pd."originalFileName",
        pd."mimeType",
        f.name as "folderName",
        ts_rank(to_tsvector('french', dc.content), plainto_tsquery('french', ${searchTerms.join(" ")})) as rank
      FROM "DocumentChunk" dc
      JOIN "ProjectDocument" pd ON dc."projectDocumentId" = pd.id
      LEFT JOIN "Folder" f ON pd."folderId" = f.id
      WHERE pd."projectId" = ${projectId}
        AND pd.status = 'READY'
        AND to_tsvector('french', dc.content) @@ plainto_tsquery('french', ${searchTerms.join(" ")})
      ORDER BY rank DESC
      LIMIT 15
    `

    for (const chunk of chunks) {
      const metadata = chunk.metadata as ChunkMetadata
      const rank = chunk.rank || 0
      let similarity = Math.min(rank * 3, 1.0) // Convert rank to similarity

      // Boost for French construction content
      if (metadata?.language === "fr" && metadata?.domain === "construction") {
        similarity += 0.1
      }

      const frenchTermsMatched = analyzedQuery.frenchTerms.filter((term) => chunk.content.toLowerCase().includes(term))

      if (similarity > 0.2) {
        results.push({
          id: chunk.id,
          content: chunk.content,
          documentName: chunk.originalFileName,
          pageNumber: chunk.pageNumber || 1,
          section: metadata?.section,
          similarity,
          matchType: "contextual_french",
          relevanceScore: similarity * 0.7,
          frenchTermsMatched,
          constructionContext: this.extractConstructionContext(chunk.content),
          sourceAttribution: {
            fileName: chunk.originalFileName,
            page: chunk.pageNumber || 1,
            section: metadata?.section,
            blockType: metadata?.blockType,
          },
          extractedPassages: this.extractRelevantPassages(chunk.content, analyzedQuery.originalQuery),
          confidence: analyzedQuery.confidence * similarity,
        })
      }
    }

    return results
  }

  /**
   * Deduplicate search results
   */
  private deduplicateResults(results: FrenchSearchResult[]): FrenchSearchResult[] {
    const seen = new Set<string>()
    const deduplicated: FrenchSearchResult[] = []

    for (const result of results) {
      if (!seen.has(result.id)) {
        seen.add(result.id)
        deduplicated.push(result)
      } else {
        // If we've seen this chunk, update the existing result if this one is better
        const existingIndex = deduplicated.findIndex((r) => r.id === result.id)
        if (existingIndex !== -1 && result.relevanceScore > deduplicated[existingIndex].relevanceScore) {
          deduplicated[existingIndex] = result
        }
      }
    }

    return deduplicated
  }

  /**
   * Rank French search results with construction domain optimization
   */
  private rankFrenchResults(
    results: FrenchSearchResult[],
    analyzedQuery: FrenchConstructionSearchQuery
  ): FrenchSearchResult[] {
    return results.sort((a, b) => {
      // Primary sort: relevance score
      if (Math.abs(a.relevanceScore - b.relevanceScore) > 0.1) {
        return b.relevanceScore - a.relevanceScore
      }

      // Secondary sort: French terms matched
      const aFrenchScore = a.frenchTermsMatched.length / Math.max(analyzedQuery.frenchTerms.length, 1)
      const bFrenchScore = b.frenchTermsMatched.length / Math.max(analyzedQuery.frenchTerms.length, 1)

      if (Math.abs(aFrenchScore - bFrenchScore) > 0.1) {
        return bFrenchScore - aFrenchScore
      }

      // Tertiary sort: match type priority
      const matchTypePriority = {
        exact_french: 4,
        technical_french: 3,
        semantic_french: 2,
        contextual_french: 1,
      }

      const aPriority = matchTypePriority[a.matchType] || 0
      const bPriority = matchTypePriority[b.matchType] || 0

      if (aPriority !== bPriority) {
        return bPriority - aPriority
      }

      // Final sort: confidence
      return b.confidence - a.confidence
    })
  }

  /**
   * Extract construction context from content
   */
  private extractConstructionContext(content: string): string {
    const contexts: string[] = []
    const contentLower = content.toLowerCase()

    // Find construction categories present
    Object.entries(FRENCH_CONSTRUCTION_DICTIONARY).forEach(([category, terms]) => {
      const foundTerms = terms.filter((term) => contentLower.includes(term))
      if (foundTerms.length > 0) {
        contexts.push(`${category}: ${foundTerms.slice(0, 2).join(", ")}`)
      }
    })

    return contexts.slice(0, 3).join(" | ") || "Construction générale"
  }

  /**
   * Extract relevant passages from content
   */
  private extractRelevantPassages(content: string, query: string): string[] {
    const passages: string[] = []
    const sentences = content.split(/[.!?]+/).filter((s) => s.trim().length > 10)
    const queryLower = query.toLowerCase()

    for (const sentence of sentences) {
      const sentenceLower = sentence.toLowerCase()

      // Check if sentence contains query terms
      if (
        sentenceLower.includes(queryLower) ||
        query.split(/\s+/).some((word) => word.length > 2 && sentenceLower.includes(word.toLowerCase()))
      ) {
        passages.push(sentence.trim())
      }
    }

    // If no direct matches, return first few sentences
    if (passages.length === 0) {
      passages.push(...sentences.slice(0, 2).map((s) => s.trim()))
    }

    return passages.slice(0, 3) // Limit to 3 passages
  }
}
