import { generateEmbeddings } from "@/lib/ai/embeddings"
import { prisma } from "@/lib/prisma"
import { ChunkMetadata, DocumentChunk } from "@/types/project"
import { logger } from "@buildismart/lib"
import { createId } from "@paralleldrive/cuid2"

export interface ChunkingOptions {
  maxChunkSize: number
  overlapSize: number
  preserveParagraphs: boolean
  preserveSentences: boolean
  minChunkSize: number
}

export interface ChunkingResult {
  chunks: DocumentChunk[]
  totalChunks: number
  processingTime: number
}

// Enhanced chunking options optimized for French construction documents
const DEFAULT_CHUNKING_OPTIONS: ChunkingOptions = {
  maxChunkSize: 800, // Increased for better context in French construction docs
  overlapSize: 200, // Larger overlap for French sentence structure
  preserveParagraphs: true,
  preserveSentences: true,
  minChunkSize: 100, // Larger minimum for meaningful French content
}

// Optimized for precise French construction terminology retrieval
const GRANULAR_CHUNKING_OPTIONS: ChunkingOptions = {
  maxChunkSize: 400, // Balanced size for French technical content
  overlapSize: 150, // High overlap for French context preservation
  preserveParagraphs: false, // Allow sentence-level chunking
  preserveSentences: true,
  minChunkSize: 80, // Meaningful minimum for French sentences
}

// Sentence-level chunking for exact French phrase matching
const SENTENCE_CHUNKING_OPTIONS: ChunkingOptions = {
  maxChunkSize: 300, // Single sentence with context
  overlapSize: 100, // Context from adjacent sentences
  preserveParagraphs: false,
  preserveSentences: true,
  minChunkSize: 50, // Minimum for French sentence fragments
}

// French construction document patterns for enhanced recognition
const FRENCH_CONSTRUCTION_PATTERNS = {
  sections: [
    /(?:^|\n)\s*(?:ARTICLE|ART\.?)\s+\d+/gi,
    /(?:^|\n)\s*(?:CHAPITRE|CHAP\.?)\s+\d+/gi,
    /(?:^|\n)\s*(?:SECTION|SECT\.?)\s+\d+/gi,
    /(?:^|\n)\s*(?:TITRE|TIT\.?)\s+\d+/gi,
    /(?:^|\n)\s*\d+\.\s*[A-ZÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞ]/gm,
    /(?:^|\n)\s*[A-ZÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞ]\.\s*[A-ZÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞ]/gm,
  ],
  sentences: [
    /[.!?]+\s+(?=[A-ZÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞ])/g, // French sentence boundaries
    /[.!?]+\s*$/gm, // End of paragraph sentences
  ],
  technicalBlocks: [
    /(?:CCTP|DPGF|DTU)\s*[:\-]?[^.]*[.]/gi,
    /(?:Norme|Réglementation|Spécification)\s*[:\-]?[^.]*[.]/gi,
    /(?:Matériau|Matériaux|Fourniture|Fournitures)\s*[:\-]?[^.]*[.]/gi,
  ],
}

/**
 * Enhanced text chunking strategy for French construction documents
 */
export class DocumentChunker {
  private options: ChunkingOptions

  constructor(options: Partial<ChunkingOptions> = {}) {
    this.options = { ...DEFAULT_CHUNKING_OPTIONS, ...options }
  }

  /**
   * Detect if content is French construction document
   */
  private detectFrenchConstructionContent(text: string): boolean {
    const frenchTerms = [
      "béton",
      "armé",
      "maçonnerie",
      "charpente",
      "isolation",
      "étanchéité",
      "cctp",
      "dpgf",
      "dtu",
      "norme",
      "réglementation",
      "article",
      "chapitre",
    ]

    const textLower = text.toLowerCase()
    const matchCount = frenchTerms.filter((term) => textLower.includes(term)).length

    return matchCount >= 2 // At least 2 French construction terms
  }

  /**
   * Create optimized chunking strategies for French construction documents
   */
  public createMultipleChunkingStrategies(
    text: string,
    fileName: string
  ): Array<{ content: string; metadata: Partial<ChunkMetadata> }> {
    const allChunks: Array<{ content: string; metadata: Partial<ChunkMetadata> }> = []
    const isFrenchConstruction = this.detectFrenchConstructionContent(text)

    if (isFrenchConstruction) {
      // Strategy 1: Optimized standard chunking for French construction
      const standardChunks = this.chunkText(text, fileName)
      standardChunks.forEach((chunk) => {
        chunk.metadata.strategy = "french_standard"
        chunk.metadata.language = "fr"
        chunk.metadata.domain = "construction"
        allChunks.push(chunk)
      })

      // Strategy 2: Technical block chunking for French construction terms
      const technicalChunks = this.createTechnicalBlockChunks(text, fileName)
      technicalChunks.forEach((chunk) => {
        chunk.metadata.strategy = "french_technical"
        chunk.metadata.language = "fr"
        chunk.metadata.domain = "construction"
        allChunks.push(chunk)
      })
    } else {
      // Fallback to standard chunking for non-French content
      const standardChunks = this.chunkText(text, fileName)
      standardChunks.forEach((chunk) => {
        chunk.metadata.strategy = "standard"
        allChunks.push(chunk)
      })

      // Granular chunking for precise retrieval
      const granularChunker = new DocumentChunker(GRANULAR_CHUNKING_OPTIONS)
      const granularChunks = granularChunker.chunkText(text, fileName)
      granularChunks.forEach((chunk) => {
        chunk.metadata.strategy = "granular"
        allChunks.push(chunk)
      })
    }

    return allChunks
  }

  /**
   * Create technical block chunks for French construction documents
   */
  private createTechnicalBlockChunks(
    text: string,
    fileName: string
  ): Array<{ content: string; metadata: Partial<ChunkMetadata> }> {
    const chunks: Array<{ content: string; metadata: Partial<ChunkMetadata> }> = []

    // Extract technical blocks using French construction patterns
    const technicalBlocks = this.extractTechnicalBlocks(text)

    technicalBlocks.forEach((block, index) => {
      if (block.content.length >= this.options.minChunkSize) {
        chunks.push({
          content: block.content,
          metadata: {
            chunkIndex: index,
            blockType: block.type,
            strategy: "french_technical",
            language: "fr",
            domain: "construction",
            startPosition: block.startPosition,
            endPosition: block.endPosition,
          },
        })
      }
    })

    return chunks
  }

  /**
   * Extract technical blocks from French construction documents
   */
  private extractTechnicalBlocks(text: string): Array<{
    content: string
    type: string
    startPosition: number
    endPosition: number
  }> {
    const blocks: Array<{
      content: string
      type: string
      startPosition: number
      endPosition: number
    }> = []

    // Extract CCTP/DPGF/DTU blocks
    FRENCH_CONSTRUCTION_PATTERNS.technicalBlocks.forEach((pattern, patternIndex) => {
      let match
      while ((match = pattern.exec(text)) !== null) {
        const content = match[0]
        const type =
          patternIndex === 0 ? "cctp_dpgf_dtu" : patternIndex === 1 ? "norme_regulation" : "materiau_fourniture"

        blocks.push({
          content: content.trim(),
          type,
          startPosition: match.index,
          endPosition: match.index + content.length,
        })
      }
    })

    // Sort by position and merge overlapping blocks
    blocks.sort((a, b) => a.startPosition - b.startPosition)

    return this.mergeOverlappingBlocks(blocks)
  }

  /**
   * Merge overlapping technical blocks
   */
  private mergeOverlappingBlocks(
    blocks: Array<{
      content: string
      type: string
      startPosition: number
      endPosition: number
    }>
  ): Array<{
    content: string
    type: string
    startPosition: number
    endPosition: number
  }> {
    if (blocks.length <= 1) return blocks

    const merged: Array<{
      content: string
      type: string
      startPosition: number
      endPosition: number
    }> = []

    let current = blocks[0]

    for (let i = 1; i < blocks.length; i++) {
      const next = blocks[i]

      // If blocks overlap or are very close, merge them
      if (next.startPosition <= current.endPosition + 50) {
        current = {
          content: current.content + " " + next.content,
          type: current.type, // Keep the first type
          startPosition: current.startPosition,
          endPosition: Math.max(current.endPosition, next.endPosition),
        }
      } else {
        merged.push(current)
        current = next
      }
    }

    merged.push(current)
    return merged
  }

  /**
   * Create sentence-level chunks for precise retrieval
   */
  private createSentenceChunks(
    text: string,
    _fileName: string
  ): Array<{ content: string; metadata: Partial<ChunkMetadata> }> {
    const sentences = text.split(/[.!?]+/).filter((s) => s.trim().length > 10)
    const chunks: Array<{ content: string; metadata: Partial<ChunkMetadata> }> = []

    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i].trim()
      if (sentence.length > 10) {
        // Include context from previous and next sentences
        const contextBefore = i > 0 ? sentences[i - 1].trim() + ". " : ""
        const contextAfter = i < sentences.length - 1 ? ". " + sentences[i + 1].trim() : ""

        chunks.push({
          content: contextBefore + sentence + contextAfter,
          metadata: {
            chunkIndex: i,
            sentenceIndex: i,
            strategy: "sentence",
            startPosition: 0, // Would need to calculate actual position
            endPosition: sentence.length,
          },
        })
      }
    }

    return chunks
  }

  /**
   * Split text into semantic chunks
   */
  public chunkText(text: string, fileName: string): Array<{ content: string; metadata: Partial<ChunkMetadata> }> {
    const startTime = Date.now()

    // Clean and normalize text
    const cleanText = this.cleanText(text)

    // Split by construction document sections first
    const sections = this.splitByConstructionSections(cleanText)

    const chunks: Array<{ content: string; metadata: Partial<ChunkMetadata> }> = []
    let globalIndex = 0

    for (const section of sections) {
      const sectionChunks = this.chunkSection(section.content, section.type, globalIndex)
      chunks.push(...sectionChunks)
      globalIndex += sectionChunks.length
    }

    logger.log(`Chunking completed in ${Date.now() - startTime}ms`, {
      fileName,
      totalChunks: chunks.length,
      avgChunkSize: chunks.reduce((sum, chunk) => sum + chunk.content.length, 0) / chunks.length,
    })

    return chunks
  }

  /**
   * Clean and normalize text
   */
  private cleanText(text: string): string {
    return text
      .replace(/\r\n/g, "\n") // Normalize line endings
      .replace(/\n{3,}/g, "\n\n") // Reduce multiple newlines
      .replace(/\s{2,}/g, " ") // Reduce multiple spaces
      .replace(/\t/g, " ") // Replace tabs with spaces
      .trim()
  }

  /**
   * Split text by construction document sections
   */
  private splitByConstructionSections(text: string): Array<{ content: string; type: string }> {
    const sections: Array<{ content: string; type: string }> = []

    // Common construction document section patterns
    const sectionPatterns = [
      /(?:^|\n)\s*(?:ARTICLE|ART\.?)\s+\d+/gi,
      /(?:^|\n)\s*(?:CHAPITRE|CHAP\.?)\s+\d+/gi,
      /(?:^|\n)\s*(?:SECTION|SECT\.?)\s+\d+/gi,
      /(?:^|\n)\s*(?:TITRE|TIT\.?)\s+\d+/gi,
      /(?:^|\n)\s*\d+\.\s*[A-Z][^.]*$/gm,
      /(?:^|\n)\s*[A-Z]\.\s*[A-Z][^.]*$/gm,
    ]

    let currentSection = { content: "", type: "general" }
    const lines = text.split("\n")

    for (const line of lines) {
      let isNewSection = false

      for (const pattern of sectionPatterns) {
        if (pattern.test(line)) {
          // Save current section if it has content
          if (currentSection.content.trim()) {
            sections.push({ ...currentSection })
          }

          // Start new section
          currentSection = {
            content: line + "\n",
            type: this.identifySectionType(line),
          }
          isNewSection = true
          break
        }
      }

      if (!isNewSection) {
        currentSection.content += line + "\n"
      }
    }

    // Add the last section
    if (currentSection.content.trim()) {
      sections.push(currentSection)
    }

    return sections.length > 0 ? sections : [{ content: text, type: "general" }]
  }

  /**
   * Identify section type based on content
   */
  private identifySectionType(line: string): string {
    const lowerLine = line.toLowerCase()

    if (lowerLine.includes("cctp") || lowerLine.includes("cahier des clauses")) return "cctp"
    if (lowerLine.includes("dpgf") || lowerLine.includes("détail")) return "dpgf"
    if (lowerLine.includes("plan") || lowerLine.includes("dessin")) return "plan"
    if (lowerLine.includes("devis") || lowerLine.includes("prix")) return "devis"
    if (lowerLine.includes("règlement") || lowerLine.includes("règle")) return "reglement"
    if (lowerLine.includes("article")) return "article"
    if (lowerLine.includes("chapitre")) return "chapitre"
    if (lowerLine.includes("section")) return "section"

    return "general"
  }

  /**
   * Chunk a section into smaller pieces
   */
  private chunkSection(
    content: string,
    sectionType: string,
    startIndex: number
  ): Array<{ content: string; metadata: Partial<ChunkMetadata> }> {
    const chunks: Array<{ content: string; metadata: Partial<ChunkMetadata> }> = []

    if (content.length <= this.options.maxChunkSize) {
      // Section is small enough to be a single chunk
      chunks.push({
        content: content.trim(),
        metadata: {
          chunkIndex: startIndex,
          section: sectionType,
          startPosition: 0,
          endPosition: content.length,
        },
      })
      return chunks
    }

    // Split by paragraphs first
    const paragraphs = content.split(/\n\s*\n/).filter((p) => p.trim().length > 0)

    let currentChunk = ""
    let chunkIndex = startIndex
    let position = 0

    for (const paragraph of paragraphs) {
      const paragraphWithNewline = paragraph + "\n\n"

      // If adding this paragraph would exceed max size, save current chunk
      if (currentChunk.length + paragraphWithNewline.length > this.options.maxChunkSize && currentChunk.length > 0) {
        if (currentChunk.trim().length >= this.options.minChunkSize) {
          chunks.push({
            content: currentChunk.trim(),
            metadata: {
              chunkIndex,
              section: sectionType,
              startPosition: position - currentChunk.length,
              endPosition: position,
            },
          })
          chunkIndex++
        }

        // Start new chunk with overlap if needed
        currentChunk = this.getOverlapText(currentChunk) + paragraphWithNewline
      } else {
        currentChunk += paragraphWithNewline
      }

      position += paragraphWithNewline.length
    }

    // Add the last chunk
    if (currentChunk.trim().length >= this.options.minChunkSize) {
      chunks.push({
        content: currentChunk.trim(),
        metadata: {
          chunkIndex,
          section: sectionType,
          startPosition: position - currentChunk.length,
          endPosition: position,
        },
      })
    }

    return chunks
  }

  /**
   * Get overlap text from the end of a chunk
   */
  private getOverlapText(text: string): string {
    if (text.length <= this.options.overlapSize) return text

    const overlapText = text.slice(-this.options.overlapSize)

    // Try to break at sentence boundary
    const sentenceEnd = overlapText.lastIndexOf(". ")
    if (sentenceEnd > this.options.overlapSize / 2) {
      return overlapText.slice(sentenceEnd + 2)
    }

    // Try to break at word boundary
    const wordBoundary = overlapText.lastIndexOf(" ")
    if (wordBoundary > this.options.overlapSize / 2) {
      return overlapText.slice(wordBoundary + 1)
    }

    return overlapText
  }
}

/**
 * Process document chunks and store them in database
 */
export async function chunkAndEmbedDocument(
  documentId: string,
  options: Partial<ChunkingOptions> = {}
): Promise<ChunkingResult> {
  const startTime = Date.now()

  try {
    // Get document
    const document = await prisma.projectDocument.findUnique({
      where: { id: documentId },
      include: { file: true },
    })

    if (!document?.extractedText) {
      throw new Error("No extracted text found for document")
    }

    // Update status
    await prisma.projectDocument.update({
      where: { id: documentId },
      data: { status: "INDEXING_CHUNKS" },
    })

    // Create chunker and process text with multiple strategies
    const chunker = new DocumentChunker(options)
    const textChunks = chunker.createMultipleChunkingStrategies(document.extractedText, document.originalFileName)

    if (textChunks.length === 0) {
      throw new Error("No chunks generated from document")
    }

    // Generate embeddings
    const contents = textChunks.map((chunk) => chunk.content)
    const embeddings = await generateEmbeddings(contents)

    // Prepare database operations
    const now = new Date()

    // Delete existing chunks
    await prisma.documentChunk.deleteMany({
      where: { projectDocumentId: documentId },
    })

    // Insert new chunks
    const chunkRecords = textChunks.map((chunk, index) => ({
      id: createId(),
      content: chunk.content,
      embedding: embeddings[index],
      pageNumber: chunk.metadata.pageNumber || index + 1,
      metadata: {
        ...chunk.metadata,
        documentId,
        chunkIndex: chunk.metadata.chunkIndex ?? index,
        createdAt: now.toISOString(),
      },
      projectDocumentId: documentId,
      folderId: document.folderId,
      createdAt: now,
      updatedAt: now,
    }))

    // Use transaction for atomic operation
    await prisma.$transaction(
      chunkRecords.map((chunk) =>
        prisma.documentChunk.create({
          data: {
            id: chunk.id,
            content: chunk.content,
            pageNumber: chunk.pageNumber,
            metadata: chunk.metadata,
            projectDocumentId: chunk.projectDocumentId,
            folderId: chunk.folderId,
            createdAt: chunk.createdAt,
            updatedAt: chunk.updatedAt,
          },
        })
      )
    )

    // Update document status
    await prisma.projectDocument.update({
      where: { id: documentId },
      data: { status: "READY" },
    })

    const processingTime = Date.now() - startTime

    logger.log("Document chunking completed", {
      documentId,
      chunksCreated: chunkRecords.length,
      processingTime,
    })

    return {
      chunks: chunkRecords,
      totalChunks: chunkRecords.length,
      processingTime,
    }
  } catch (error) {
    logger.error("Document chunking failed", { documentId, error })

    await prisma.projectDocument.update({
      where: { id: documentId },
      data: {
        status: "ERROR",
        processingError: error instanceof Error ? error.message : "Unknown chunking error",
      },
    })

    throw error
  }
}
