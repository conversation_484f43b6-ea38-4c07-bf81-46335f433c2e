import { generateEmbeddings } from "@/lib/ai/embeddings"
import { prisma } from "@/lib/prisma"
import { ChunkMetadata, DocumentChunk } from "@/types/project"
import { logger } from "@buildismart/lib"
import { createId } from "@paralleldrive/cuid2"

export interface ChunkingOptions {
  maxChunkSize: number
  overlapSize: number
  preserveParagraphs: boolean
  preserveSentences: boolean
  minChunkSize: number
}

export interface ChunkingResult {
  chunks: DocumentChunk[]
  totalChunks: number
  processingTime: number
}

// Enhanced chunking options for better granularity and retrieval
const DEFAULT_CHUNKING_OPTIONS: ChunkingOptions = {
  maxChunkSize: 400, // Reduced from 1000 for better granularity
  overlapSize: 100, // Increased overlap for better context preservation
  preserveParagraphs: true,
  preserveSentences: true,
  minChunkSize: 50, // Reduced minimum size for more granular chunks
}

// Additional chunking options for different content types
const GRANULAR_CHUNKING_OPTIONS: ChunkingOptions = {
  maxChunkSize: 200, // Very small chunks for precise retrieval
  overlapSize: 80, // High overlap for context
  preserveParagraphs: false, // Allow sentence-level chunking
  preserveSentences: true,
  minChunkSize: 30,
}

/**
 * Improved text chunking strategy for construction documents
 */
export class DocumentChunker {
  private options: ChunkingOptions

  constructor(options: Partial<ChunkingOptions> = {}) {
    this.options = { ...DEFAULT_CHUNKING_OPTIONS, ...options }
  }

  /**
   * Create multiple chunking strategies for comprehensive coverage
   */
  public createMultipleChunkingStrategies(
    text: string,
    fileName: string
  ): Array<{ content: string; metadata: Partial<ChunkMetadata> }> {
    const allChunks: Array<{ content: string; metadata: Partial<ChunkMetadata> }> = []

    // Strategy 1: Standard chunking
    const standardChunks = this.chunkText(text, fileName)
    standardChunks.forEach((chunk) => {
      chunk.metadata.strategy = "standard"
      allChunks.push(chunk)
    })

    // Strategy 2: Granular chunking for precise retrieval
    const granularChunker = new DocumentChunker(GRANULAR_CHUNKING_OPTIONS)
    const granularChunks = granularChunker.chunkText(text, fileName)
    granularChunks.forEach((chunk) => {
      chunk.metadata.strategy = "granular"
      allChunks.push(chunk)
    })

    // Strategy 3: Sentence-level chunking for exact matches
    const sentenceChunks = this.createSentenceChunks(text, fileName)
    sentenceChunks.forEach((chunk) => {
      chunk.metadata.strategy = "sentence"
      allChunks.push(chunk)
    })

    return allChunks
  }

  /**
   * Create sentence-level chunks for precise retrieval
   */
  private createSentenceChunks(
    text: string,
    _fileName: string
  ): Array<{ content: string; metadata: Partial<ChunkMetadata> }> {
    const sentences = text.split(/[.!?]+/).filter((s) => s.trim().length > 10)
    const chunks: Array<{ content: string; metadata: Partial<ChunkMetadata> }> = []

    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i].trim()
      if (sentence.length > 10) {
        // Include context from previous and next sentences
        const contextBefore = i > 0 ? sentences[i - 1].trim() + ". " : ""
        const contextAfter = i < sentences.length - 1 ? ". " + sentences[i + 1].trim() : ""

        chunks.push({
          content: contextBefore + sentence + contextAfter,
          metadata: {
            chunkIndex: i,
            sentenceIndex: i,
            strategy: "sentence",
            startPosition: 0, // Would need to calculate actual position
            endPosition: sentence.length,
          },
        })
      }
    }

    return chunks
  }

  /**
   * Split text into semantic chunks
   */
  public chunkText(text: string, fileName: string): Array<{ content: string; metadata: Partial<ChunkMetadata> }> {
    const startTime = Date.now()

    // Clean and normalize text
    const cleanText = this.cleanText(text)

    // Split by construction document sections first
    const sections = this.splitByConstructionSections(cleanText)

    const chunks: Array<{ content: string; metadata: Partial<ChunkMetadata> }> = []
    let globalIndex = 0

    for (const section of sections) {
      const sectionChunks = this.chunkSection(section.content, section.type, globalIndex)
      chunks.push(...sectionChunks)
      globalIndex += sectionChunks.length
    }

    logger.log(`Chunking completed in ${Date.now() - startTime}ms`, {
      fileName,
      totalChunks: chunks.length,
      avgChunkSize: chunks.reduce((sum, chunk) => sum + chunk.content.length, 0) / chunks.length,
    })

    return chunks
  }

  /**
   * Clean and normalize text
   */
  private cleanText(text: string): string {
    return text
      .replace(/\r\n/g, "\n") // Normalize line endings
      .replace(/\n{3,}/g, "\n\n") // Reduce multiple newlines
      .replace(/\s{2,}/g, " ") // Reduce multiple spaces
      .replace(/\t/g, " ") // Replace tabs with spaces
      .trim()
  }

  /**
   * Split text by construction document sections
   */
  private splitByConstructionSections(text: string): Array<{ content: string; type: string }> {
    const sections: Array<{ content: string; type: string }> = []

    // Common construction document section patterns
    const sectionPatterns = [
      /(?:^|\n)\s*(?:ARTICLE|ART\.?)\s+\d+/gi,
      /(?:^|\n)\s*(?:CHAPITRE|CHAP\.?)\s+\d+/gi,
      /(?:^|\n)\s*(?:SECTION|SECT\.?)\s+\d+/gi,
      /(?:^|\n)\s*(?:TITRE|TIT\.?)\s+\d+/gi,
      /(?:^|\n)\s*\d+\.\s*[A-Z][^.]*$/gm,
      /(?:^|\n)\s*[A-Z]\.\s*[A-Z][^.]*$/gm,
    ]

    let currentSection = { content: "", type: "general" }
    const lines = text.split("\n")

    for (const line of lines) {
      let isNewSection = false

      for (const pattern of sectionPatterns) {
        if (pattern.test(line)) {
          // Save current section if it has content
          if (currentSection.content.trim()) {
            sections.push({ ...currentSection })
          }

          // Start new section
          currentSection = {
            content: line + "\n",
            type: this.identifySectionType(line),
          }
          isNewSection = true
          break
        }
      }

      if (!isNewSection) {
        currentSection.content += line + "\n"
      }
    }

    // Add the last section
    if (currentSection.content.trim()) {
      sections.push(currentSection)
    }

    return sections.length > 0 ? sections : [{ content: text, type: "general" }]
  }

  /**
   * Identify section type based on content
   */
  private identifySectionType(line: string): string {
    const lowerLine = line.toLowerCase()

    if (lowerLine.includes("cctp") || lowerLine.includes("cahier des clauses")) return "cctp"
    if (lowerLine.includes("dpgf") || lowerLine.includes("détail")) return "dpgf"
    if (lowerLine.includes("plan") || lowerLine.includes("dessin")) return "plan"
    if (lowerLine.includes("devis") || lowerLine.includes("prix")) return "devis"
    if (lowerLine.includes("règlement") || lowerLine.includes("règle")) return "reglement"
    if (lowerLine.includes("article")) return "article"
    if (lowerLine.includes("chapitre")) return "chapitre"
    if (lowerLine.includes("section")) return "section"

    return "general"
  }

  /**
   * Chunk a section into smaller pieces
   */
  private chunkSection(
    content: string,
    sectionType: string,
    startIndex: number
  ): Array<{ content: string; metadata: Partial<ChunkMetadata> }> {
    const chunks: Array<{ content: string; metadata: Partial<ChunkMetadata> }> = []

    if (content.length <= this.options.maxChunkSize) {
      // Section is small enough to be a single chunk
      chunks.push({
        content: content.trim(),
        metadata: {
          chunkIndex: startIndex,
          section: sectionType,
          startPosition: 0,
          endPosition: content.length,
        },
      })
      return chunks
    }

    // Split by paragraphs first
    const paragraphs = content.split(/\n\s*\n/).filter((p) => p.trim().length > 0)

    let currentChunk = ""
    let chunkIndex = startIndex
    let position = 0

    for (const paragraph of paragraphs) {
      const paragraphWithNewline = paragraph + "\n\n"

      // If adding this paragraph would exceed max size, save current chunk
      if (currentChunk.length + paragraphWithNewline.length > this.options.maxChunkSize && currentChunk.length > 0) {
        if (currentChunk.trim().length >= this.options.minChunkSize) {
          chunks.push({
            content: currentChunk.trim(),
            metadata: {
              chunkIndex,
              section: sectionType,
              startPosition: position - currentChunk.length,
              endPosition: position,
            },
          })
          chunkIndex++
        }

        // Start new chunk with overlap if needed
        currentChunk = this.getOverlapText(currentChunk) + paragraphWithNewline
      } else {
        currentChunk += paragraphWithNewline
      }

      position += paragraphWithNewline.length
    }

    // Add the last chunk
    if (currentChunk.trim().length >= this.options.minChunkSize) {
      chunks.push({
        content: currentChunk.trim(),
        metadata: {
          chunkIndex,
          section: sectionType,
          startPosition: position - currentChunk.length,
          endPosition: position,
        },
      })
    }

    return chunks
  }

  /**
   * Get overlap text from the end of a chunk
   */
  private getOverlapText(text: string): string {
    if (text.length <= this.options.overlapSize) return text

    const overlapText = text.slice(-this.options.overlapSize)

    // Try to break at sentence boundary
    const sentenceEnd = overlapText.lastIndexOf(". ")
    if (sentenceEnd > this.options.overlapSize / 2) {
      return overlapText.slice(sentenceEnd + 2)
    }

    // Try to break at word boundary
    const wordBoundary = overlapText.lastIndexOf(" ")
    if (wordBoundary > this.options.overlapSize / 2) {
      return overlapText.slice(wordBoundary + 1)
    }

    return overlapText
  }
}

/**
 * Process document chunks and store them in database
 */
export async function chunkAndEmbedDocument(
  documentId: string,
  options: Partial<ChunkingOptions> = {}
): Promise<ChunkingResult> {
  const startTime = Date.now()

  try {
    // Get document
    const document = await prisma.projectDocument.findUnique({
      where: { id: documentId },
      include: { file: true },
    })

    if (!document?.extractedText) {
      throw new Error("No extracted text found for document")
    }

    // Update status
    await prisma.projectDocument.update({
      where: { id: documentId },
      data: { status: "INDEXING_CHUNKS" },
    })

    // Create chunker and process text with multiple strategies
    const chunker = new DocumentChunker(options)
    const textChunks = chunker.createMultipleChunkingStrategies(document.extractedText, document.originalFileName)

    if (textChunks.length === 0) {
      throw new Error("No chunks generated from document")
    }

    // Generate embeddings
    const contents = textChunks.map((chunk) => chunk.content)
    const embeddings = await generateEmbeddings(contents)

    // Prepare database operations
    const now = new Date()

    // Delete existing chunks
    await prisma.documentChunk.deleteMany({
      where: { projectDocumentId: documentId },
    })

    // Insert new chunks
    const chunkRecords = textChunks.map((chunk, index) => ({
      id: createId(),
      content: chunk.content,
      embedding: embeddings[index],
      pageNumber: chunk.metadata.pageNumber || index + 1,
      metadata: {
        ...chunk.metadata,
        documentId,
        chunkIndex: chunk.metadata.chunkIndex ?? index,
        createdAt: now.toISOString(),
      },
      projectDocumentId: documentId,
      folderId: document.folderId,
      createdAt: now,
      updatedAt: now,
    }))

    // Use transaction for atomic operation
    await prisma.$transaction(
      chunkRecords.map((chunk) =>
        prisma.documentChunk.create({
          data: {
            id: chunk.id,
            content: chunk.content,
            pageNumber: chunk.pageNumber,
            metadata: chunk.metadata,
            projectDocumentId: chunk.projectDocumentId,
            folderId: chunk.folderId,
            createdAt: chunk.createdAt,
            updatedAt: chunk.updatedAt,
          },
        })
      )
    )

    // Update document status
    await prisma.projectDocument.update({
      where: { id: documentId },
      data: { status: "READY" },
    })

    const processingTime = Date.now() - startTime

    logger.log("Document chunking completed", {
      documentId,
      chunksCreated: chunkRecords.length,
      processingTime,
    })

    return {
      chunks: chunkRecords,
      totalChunks: chunkRecords.length,
      processingTime,
    }
  } catch (error) {
    logger.error("Document chunking failed", { documentId, error })

    await prisma.projectDocument.update({
      where: { id: documentId },
      data: {
        status: "ERROR",
        processingError: error instanceof Error ? error.message : "Unknown chunking error",
      },
    })

    throw error
  }
}
