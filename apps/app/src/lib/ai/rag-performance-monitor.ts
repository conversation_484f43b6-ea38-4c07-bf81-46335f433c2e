import { prisma } from "@/lib/prisma"
import { FrenchConstructionSearch } from "./french-construction-search"
import { semanticSearchInDocumentChunks } from "./embeddings"
import { logger } from "@buildismart/lib"

// Performance monitoring interfaces
export interface RAGPerformanceMetrics {
  searchId: string
  projectId: string
  query: string
  queryType: "french_construction" | "general" | "technical"
  searchMethod: "french_optimized" | "standard_semantic" | "hybrid"

  // Timing metrics
  totalTime: number
  embeddingTime: number
  searchTime: number
  rankingTime: number

  // Quality metrics
  resultsCount: number
  avgSimilarity: number
  avgConfidence: number
  frenchTermsDetected: number
  constructionTermsDetected: number

  // Relevance metrics
  topResultSimilarity: number
  precisionAtK: number // Precision at top K results
  diversityScore: number // How diverse are the results

  // User feedback (when available)
  userRating?: number // 1-5 scale
  userFeedback?: string
  clickedResults?: number[]

  timestamp: Date
}

export interface RAGTestCase {
  id: string
  name: string
  description: string
  query: string
  expectedTerms: string[]
  expectedDocuments?: string[]
  minSimilarity: number
  maxResults: number
  language: "fr" | "en" | "mixed"
  domain: "construction" | "technical" | "general"
  difficulty: "easy" | "medium" | "hard"
}

// French construction test cases
const FRENCH_CONSTRUCTION_TEST_CASES: RAGTestCase[] = [
  {
    id: "fc_001",
    name: "Béton armé - Recherche basique",
    description: "Test de recherche simple sur le béton armé",
    query: "béton armé résistance compression",
    expectedTerms: ["béton", "armé", "résistance", "compression"],
    minSimilarity: 0.7,
    maxResults: 10,
    language: "fr",
    domain: "construction",
    difficulty: "easy",
  },
  {
    id: "fc_002",
    name: "DTU - Recherche normative",
    description: "Test de recherche sur les documents techniques unifiés",
    query: "DTU 13.12 fondations profondes",
    expectedTerms: ["dtu", "fondations", "profondes"],
    expectedDocuments: ["DTU", "CCTP"],
    minSimilarity: 0.6,
    maxResults: 15,
    language: "fr",
    domain: "construction",
    difficulty: "medium",
  },
  {
    id: "fc_003",
    name: "Isolation thermique - Recherche technique",
    description: "Test de recherche complexe sur l'isolation thermique",
    query: "isolation thermique par l'extérieur ITE polystyrène épaisseur",
    expectedTerms: ["isolation", "thermique", "extérieur", "ite", "polystyrène", "épaisseur"],
    minSimilarity: 0.5,
    maxResults: 20,
    language: "fr",
    domain: "construction",
    difficulty: "hard",
  },
  {
    id: "fc_004",
    name: "Réglementation RT2020",
    description: "Test de recherche sur la réglementation thermique",
    query: "réglementation thermique RT2020 RE2020 consommation énergétique",
    expectedTerms: ["réglementation", "thermique", "rt2020", "re2020", "consommation", "énergétique"],
    minSimilarity: 0.6,
    maxResults: 12,
    language: "fr",
    domain: "construction",
    difficulty: "medium",
  },
  {
    id: "fc_005",
    name: "Charpente bois - Recherche spécialisée",
    description: "Test de recherche sur les structures bois",
    query: "charpente bois lamellé collé portée calcul charge",
    expectedTerms: ["charpente", "bois", "lamellé", "collé", "portée", "calcul", "charge"],
    minSimilarity: 0.5,
    maxResults: 15,
    language: "fr",
    domain: "construction",
    difficulty: "hard",
  },
]

/**
 * RAG Performance Monitor for French Construction Documents
 */
export class RAGPerformanceMonitor {
  private frenchSearch = new FrenchConstructionSearch()

  /**
   * Run comprehensive performance test suite
   */
  async runPerformanceTestSuite(projectId: string): Promise<{
    overallScore: number
    testResults: Array<{
      testCase: RAGTestCase
      metrics: RAGPerformanceMetrics
      passed: boolean
      issues: string[]
    }>
    recommendations: string[]
  }> {
    logger.log("Starting RAG performance test suite", { projectId })

    const testResults: Array<{
      testCase: RAGTestCase
      metrics: RAGPerformanceMetrics
      passed: boolean
      issues: string[]
    }> = []

    for (const testCase of FRENCH_CONSTRUCTION_TEST_CASES) {
      logger.log(`Running test case: ${testCase.name}`, { testCaseId: testCase.id })

      const result = await this.runSingleTest(projectId, testCase)
      testResults.push(result)
    }

    // Calculate overall performance score
    const passedTests = testResults.filter((r) => r.passed).length
    const overallScore = (passedTests / testResults.length) * 100

    // Generate recommendations
    const recommendations = this.generateRecommendations(testResults)

    logger.log("RAG performance test suite completed", {
      projectId,
      totalTests: testResults.length,
      passedTests,
      overallScore,
      recommendations: recommendations.length,
    })

    return {
      overallScore,
      testResults,
      recommendations,
    }
  }

  /**
   * Run a single performance test
   */
  async runSingleTest(
    projectId: string,
    testCase: RAGTestCase
  ): Promise<{
    testCase: RAGTestCase
    metrics: RAGPerformanceMetrics
    passed: boolean
    issues: string[]
  }> {
    const startTime = Date.now()
    const issues: string[] = []

    try {
      // Test French construction search
      const frenchResults = await this.testFrenchConstructionSearch(projectId, testCase)

      // Test standard semantic search for comparison
      const standardResults = await this.testStandardSearch(projectId, testCase)

      // Compare results and determine best method
      const bestMethod =
        frenchResults.avgSimilarity > standardResults.avgSimilarity ? "french_optimized" : "standard_semantic"

      const bestResults = bestMethod === "french_optimized" ? frenchResults : standardResults

      // Validate results against test case expectations
      const validation = this.validateResults(testCase, bestResults, issues)

      const totalTime = Date.now() - startTime

      const metrics: RAGPerformanceMetrics = {
        searchId: `test_${testCase.id}_${Date.now()}`,
        projectId,
        query: testCase.query,
        queryType: testCase.domain === "construction" ? "french_construction" : "general",
        searchMethod: bestMethod,
        totalTime,
        embeddingTime: bestResults.embeddingTime,
        searchTime: bestResults.searchTime,
        rankingTime: bestResults.rankingTime,
        resultsCount: bestResults.resultsCount,
        avgSimilarity: bestResults.avgSimilarity,
        avgConfidence: bestResults.avgConfidence,
        frenchTermsDetected: bestResults.frenchTermsDetected,
        constructionTermsDetected: bestResults.constructionTermsDetected,
        topResultSimilarity: bestResults.topResultSimilarity,
        precisionAtK: bestResults.precisionAtK,
        diversityScore: bestResults.diversityScore,
        timestamp: new Date(),
      }

      return {
        testCase,
        metrics,
        passed: validation.passed,
        issues: validation.issues,
      }
    } catch (error) {
      issues.push(`Test execution failed: ${error instanceof Error ? error.message : "Unknown error"}`)

      // Return minimal metrics for failed test
      const metrics: RAGPerformanceMetrics = {
        searchId: `test_${testCase.id}_${Date.now()}`,
        projectId,
        query: testCase.query,
        queryType: "general",
        searchMethod: "standard_semantic",
        totalTime: Date.now() - startTime,
        embeddingTime: 0,
        searchTime: 0,
        rankingTime: 0,
        resultsCount: 0,
        avgSimilarity: 0,
        avgConfidence: 0,
        frenchTermsDetected: 0,
        constructionTermsDetected: 0,
        topResultSimilarity: 0,
        precisionAtK: 0,
        diversityScore: 0,
        timestamp: new Date(),
      }

      return {
        testCase,
        metrics,
        passed: false,
        issues,
      }
    }
  }

  /**
   * Test French construction search performance
   */
  private async testFrenchConstructionSearch(projectId: string, testCase: RAGTestCase) {
    const startTime = Date.now()

    const embeddingStart = Date.now()
    const queryAnalysis = await this.frenchSearch.analyzeQuery(testCase.query)
    const embeddingTime = Date.now() - embeddingStart

    const searchStart = Date.now()
    const results = await this.frenchSearch.performFrenchConstructionSearch(projectId, testCase.query, {
      maxResults: testCase.maxResults,
      minSimilarity: testCase.minSimilarity,
      includeContext: true,
      searchStrategies: ["exact", "semantic", "technical", "contextual"],
      frenchOptimized: true,
      constructionFocus: true,
    })
    const searchTime = Date.now() - searchStart

    const rankingStart = Date.now()
    // Results are already ranked by the French search
    const rankingTime = Date.now() - rankingStart

    return {
      embeddingTime,
      searchTime,
      rankingTime,
      resultsCount: results.length,
      avgSimilarity: results.reduce((sum, r) => sum + r.similarity, 0) / Math.max(results.length, 1),
      avgConfidence: results.reduce((sum, r) => sum + r.confidence, 0) / Math.max(results.length, 1),
      frenchTermsDetected: queryAnalysis.frenchTerms.length,
      constructionTermsDetected: queryAnalysis.constructionKeywords.length,
      topResultSimilarity: results[0]?.similarity || 0,
      precisionAtK: this.calculatePrecisionAtK(results, testCase.expectedTerms, 5),
      diversityScore: this.calculateDiversityScore(results),
    }
  }

  /**
   * Test standard semantic search performance
   */
  private async testStandardSearch(projectId: string, testCase: RAGTestCase) {
    const startTime = Date.now()

    const embeddingStart = Date.now()
    // Standard search doesn't have separate query analysis
    const embeddingTime = Date.now() - embeddingStart

    const searchStart = Date.now()
    const searchResult = await semanticSearchInDocumentChunks(projectId, testCase.query, testCase.maxResults)
    const searchTime = Date.now() - searchStart

    const rankingStart = Date.now()
    // Results are already ranked
    const rankingTime = Date.now() - rankingStart

    const results = searchResult.chunks || []

    return {
      embeddingTime,
      searchTime,
      rankingTime,
      resultsCount: results.length,
      avgSimilarity: results.reduce((sum, r) => sum + (r.similarity || 0), 0) / Math.max(results.length, 1),
      avgConfidence: 0.5, // Standard search doesn't provide confidence
      frenchTermsDetected: 0, // Standard search doesn't detect French terms
      constructionTermsDetected: 0, // Standard search doesn't detect construction terms
      topResultSimilarity: results[0]?.similarity || 0,
      precisionAtK: this.calculatePrecisionAtK(results, testCase.expectedTerms, 5),
      diversityScore: this.calculateDiversityScore(results),
    }
  }

  /**
   * Validate search results against test case expectations
   */
  private validateResults(
    testCase: RAGTestCase,
    results: any,
    issues: string[]
  ): {
    passed: boolean
    issues: string[]
  } {
    let passed = true

    // Check minimum results count
    if (results.resultsCount === 0) {
      passed = false
      issues.push("No results returned")
    }

    // Check minimum similarity threshold
    if (results.avgSimilarity < testCase.minSimilarity) {
      passed = false
      issues.push(`Average similarity ${results.avgSimilarity.toFixed(3)} below threshold ${testCase.minSimilarity}`)
    }

    // Check top result similarity
    if (results.topResultSimilarity < testCase.minSimilarity) {
      passed = false
      issues.push(
        `Top result similarity ${results.topResultSimilarity.toFixed(3)} below threshold ${testCase.minSimilarity}`
      )
    }

    // Check French terms detection for French queries
    if (testCase.language === "fr" && results.frenchTermsDetected === 0) {
      issues.push("No French terms detected in French query")
    }

    // Check construction terms detection for construction domain
    if (testCase.domain === "construction" && results.constructionTermsDetected === 0) {
      issues.push("No construction terms detected in construction query")
    }

    // Check precision at K
    if (results.precisionAtK < 0.3) {
      issues.push(`Low precision at K: ${results.precisionAtK.toFixed(3)}`)
    }

    return { passed, issues }
  }

  /**
   * Calculate precision at K (top K results)
   */
  private calculatePrecisionAtK(results: any[], expectedTerms: string[], k: number): number {
    if (results.length === 0) return 0

    const topK = results.slice(0, k)
    let relevantCount = 0

    for (const result of topK) {
      const content = result.content?.toLowerCase() || ""
      const hasExpectedTerm = expectedTerms.some((term) => content.includes(term.toLowerCase()))
      if (hasExpectedTerm) {
        relevantCount++
      }
    }

    return relevantCount / Math.min(k, results.length)
  }

  /**
   * Calculate diversity score of results
   */
  private calculateDiversityScore(results: any[]): number {
    if (results.length <= 1) return 1.0

    const documents = new Set<string>()
    const sections = new Set<string>()

    for (const result of results) {
      if (result.documentName || result.originalFileName) {
        documents.add(result.documentName || result.originalFileName)
      }
      if (result.section) {
        sections.add(result.section)
      }
    }

    // Diversity based on document and section variety
    const documentDiversity = documents.size / results.length
    const sectionDiversity = sections.size / Math.max(results.length, 1)

    return (documentDiversity + sectionDiversity) / 2
  }

  /**
   * Generate recommendations based on test results
   */
  private generateRecommendations(
    testResults: Array<{
      testCase: RAGTestCase
      metrics: RAGPerformanceMetrics
      passed: boolean
      issues: string[]
    }>
  ): string[] {
    const recommendations: string[] = []

    // Analyze overall performance patterns
    const failedTests = testResults.filter((r) => !r.passed)
    const avgSimilarity = testResults.reduce((sum, r) => sum + r.metrics.avgSimilarity, 0) / testResults.length
    const avgConfidence = testResults.reduce((sum, r) => sum + r.metrics.avgConfidence, 0) / testResults.length
    const avgTime = testResults.reduce((sum, r) => sum + r.metrics.totalTime, 0) / testResults.length

    // Performance recommendations
    if (avgSimilarity < 0.6) {
      recommendations.push(
        "🔍 Similarité moyenne faible - Considérer l'amélioration des embeddings ou l'ajustement des seuils"
      )
    }

    if (avgConfidence < 0.5) {
      recommendations.push(
        "🎯 Confiance moyenne faible - Améliorer la détection des termes français et de construction"
      )
    }

    if (avgTime > 5000) {
      recommendations.push(
        "⚡ Temps de réponse élevé - Optimiser les requêtes de base de données et la taille des embeddings"
      )
    }

    // French language specific recommendations
    const frenchIssues = failedTests.filter((t) => t.issues.some((issue) => issue.includes("French terms")))
    if (frenchIssues.length > 0) {
      recommendations.push(
        "🇫🇷 Améliorer la détection des termes français - Enrichir le dictionnaire de construction français"
      )
    }

    // Construction domain recommendations
    const constructionIssues = failedTests.filter((t) => t.issues.some((issue) => issue.includes("construction terms")))
    if (constructionIssues.length > 0) {
      recommendations.push("🏗️ Améliorer la détection des termes de construction - Étendre la terminologie technique")
    }

    // Precision recommendations
    const precisionIssues = failedTests.filter((t) => t.issues.some((issue) => issue.includes("precision")))
    if (precisionIssues.length > 0) {
      recommendations.push("📊 Améliorer la précision - Affiner les algorithmes de ranking et de pertinence")
    }

    // Similarity threshold recommendations
    const similarityIssues = failedTests.filter((t) => t.issues.some((issue) => issue.includes("similarity")))
    if (similarityIssues.length > 0) {
      recommendations.push(
        "📈 Ajuster les seuils de similarité - Calibrer selon le type de contenu et la difficulté des requêtes"
      )
    }

    return recommendations
  }

  /**
   * Save performance metrics to database for tracking
   */
  async savePerformanceMetrics(metrics: RAGPerformanceMetrics): Promise<void> {
    try {
      // Note: This would require a new table in the database schema
      // For now, we'll log the metrics
      logger.log("RAG Performance Metrics", {
        searchId: metrics.searchId,
        projectId: metrics.projectId,
        query: metrics.query,
        searchMethod: metrics.searchMethod,
        totalTime: metrics.totalTime,
        resultsCount: metrics.resultsCount,
        avgSimilarity: metrics.avgSimilarity,
        avgConfidence: metrics.avgConfidence,
        frenchTermsDetected: metrics.frenchTermsDetected,
        constructionTermsDetected: metrics.constructionTermsDetected,
        topResultSimilarity: metrics.topResultSimilarity,
        precisionAtK: metrics.precisionAtK,
        diversityScore: metrics.diversityScore,
      })
    } catch (error) {
      logger.error("Failed to save performance metrics", { error, metrics })
    }
  }

  /**
   * Generate performance report
   */
  generatePerformanceReport(
    testResults: Array<{
      testCase: RAGTestCase
      metrics: RAGPerformanceMetrics
      passed: boolean
      issues: string[]
    }>
  ): string {
    const passedTests = testResults.filter((r) => r.passed).length
    const overallScore = (passedTests / testResults.length) * 100

    let report = `# 📊 Rapport de Performance RAG\n\n`
    report += `**Score Global:** ${overallScore.toFixed(1)}% (${passedTests}/${testResults.length} tests réussis)\n\n`

    report += `## 📈 Métriques Globales\n`
    const avgSimilarity = testResults.reduce((sum, r) => sum + r.metrics.avgSimilarity, 0) / testResults.length
    const avgConfidence = testResults.reduce((sum, r) => sum + r.metrics.avgConfidence, 0) / testResults.length
    const avgTime = testResults.reduce((sum, r) => sum + r.metrics.totalTime, 0) / testResults.length

    report += `- **Similarité moyenne:** ${(avgSimilarity * 100).toFixed(1)}%\n`
    report += `- **Confiance moyenne:** ${(avgConfidence * 100).toFixed(1)}%\n`
    report += `- **Temps moyen:** ${avgTime.toFixed(0)}ms\n\n`

    report += `## 🧪 Résultats des Tests\n`
    for (const result of testResults) {
      const status = result.passed ? "✅" : "❌"
      report += `${status} **${result.testCase.name}**\n`
      report += `   - Similarité: ${(result.metrics.avgSimilarity * 100).toFixed(1)}%\n`
      report += `   - Résultats: ${result.metrics.resultsCount}\n`
      report += `   - Temps: ${result.metrics.totalTime}ms\n`

      if (result.issues.length > 0) {
        report += `   - Issues: ${result.issues.join(", ")}\n`
      }
      report += `\n`
    }

    return report
  }
}
