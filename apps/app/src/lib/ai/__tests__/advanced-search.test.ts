import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { AdvancedSemanticSearch } from '../advanced-search'
import { prisma } from '@/lib/prisma'

describe('Advanced Semantic Search System', () => {
  const testProjectId = 'test-project-advanced'
  const testDocumentId = 'test-document-advanced'
  let advancedSearch: AdvancedSemanticSearch

  beforeEach(async () => {
    advancedSearch = new AdvancedSemanticSearch()
    
    // Clean up any existing test data
    await prisma.documentChunk.deleteMany({
      where: { projectDocumentId: testDocumentId }
    })
    await prisma.projectDocument.deleteMany({
      where: { id: testDocumentId }
    })
    await prisma.project.deleteMany({
      where: { id: testProjectId }
    })
  })

  afterEach(async () => {
    // Clean up test data
    await prisma.documentChunk.deleteMany({
      where: { projectDocumentId: testDocumentId }
    })
    await prisma.projectDocument.deleteMany({
      where: { id: testDocumentId }
    })
    await prisma.project.deleteMany({
      where: { id: testProjectId }
    })
  })

  describe('Query Analysis', () => {
    it('should analyze and expand construction-related queries', async () => {
      const query = "config des couleurs du boilerplate"
      const result = await advancedSearch.analyzeQuery(query)

      expect(result.originalQuery).toBe(query)
      expect(result.expandedQueries).toContain("configuration couleurs")
      expect(result.keywords).toContain("couleurs")
      expect(result.domain).toBe("technical")
    })

    it('should handle construction terminology', async () => {
      const query = "prix du béton C25/30"
      const result = await advancedSearch.analyzeQuery(query)

      expect(result.domain).toBe("construction")
      expect(result.keywords).toContain("béton")
      expect(result.expandedQueries.length).toBeGreaterThan(0)
    })

    it('should identify financial queries', async () => {
      const query = "devis et budget travaux"
      const result = await advancedSearch.analyzeQuery(query)

      expect(result.domain).toBe("financial")
      expect(result.keywords).toContain("devis")
    })
  })

  describe('Multi-Strategy Search', () => {
    it('should configure search strategies based on requirements', async () => {
      // Mock the database calls since we don't have real data
      const mockResults = []

      // Test exact search requirement
      const exactResults = await advancedSearch.performAdvancedSearch(
        testProjectId,
        "test query",
        {
          maxResults: 10,
          minSimilarity: 0.8,
          includeContext: true,
          searchStrategies: ["exact", "keyword"],
        }
      )

      expect(exactResults).toBeDefined()
      expect(Array.isArray(exactResults)).toBe(true)
    })

    it('should handle semantic search with contextual expansion', async () => {
      const semanticResults = await advancedSearch.performAdvancedSearch(
        testProjectId,
        "configuration couleurs",
        {
          maxResults: 20,
          minSimilarity: 0.3,
          includeContext: true,
          searchStrategies: ["exact", "semantic", "contextual", "keyword"],
        }
      )

      expect(semanticResults).toBeDefined()
      expect(Array.isArray(semanticResults)).toBe(true)
    })
  })

  describe('Contextual Term Generation', () => {
    it('should generate construction domain synonyms', async () => {
      const query = {
        originalQuery: "couleur béton",
        expandedQueries: ["couleur béton"],
        keywords: ["couleur", "béton"],
        context: "",
        domain: "construction" as const
      }

      const contextualTerms = await advancedSearch['generateContextualTerms'](query)
      
      expect(contextualTerms).toContain("palette")
      expect(contextualTerms).toContain("concrete")
    })

    it('should generate technical domain synonyms', async () => {
      const query = {
        originalQuery: "interface système",
        expandedQueries: ["interface système"],
        keywords: ["interface", "système"],
        context: "",
        domain: "technical" as const
      }

      const contextualTerms = await advancedSearch['generateContextualTerms'](query)
      
      expect(contextualTerms).toContain("UI")
      expect(contextualTerms).toContain("architecture")
    })
  })

  describe('Result Ranking and Deduplication', () => {
    it('should deduplicate results based on content similarity', () => {
      const duplicateResults = [
        {
          id: '1',
          content: 'Same content here',
          documentName: 'doc1.pdf',
          pageNumber: 1,
          similarity: 0.9,
          matchType: 'exact' as const,
          relevanceScore: 0.9,
          sourceAttribution: { fileName: 'doc1.pdf', page: 1 },
          extractedPassages: ['Same content here']
        },
        {
          id: '2',
          content: 'Same content here',
          documentName: 'doc1.pdf',
          pageNumber: 1,
          similarity: 0.8,
          matchType: 'semantic' as const,
          relevanceScore: 0.8,
          sourceAttribution: { fileName: 'doc1.pdf', page: 1 },
          extractedPassages: ['Same content here']
        },
        {
          id: '3',
          content: 'Different content',
          documentName: 'doc2.pdf',
          pageNumber: 2,
          similarity: 0.7,
          matchType: 'contextual' as const,
          relevanceScore: 0.7,
          sourceAttribution: { fileName: 'doc2.pdf', page: 2 },
          extractedPassages: ['Different content']
        }
      ]

      const deduplicated = advancedSearch['deduplicateResults'](duplicateResults)
      
      expect(deduplicated).toHaveLength(2)
      expect(deduplicated[0].content).toBe('Same content here')
      expect(deduplicated[1].content).toBe('Different content')
    })

    it('should rank results with multiple factors', () => {
      const query = {
        originalQuery: "béton",
        expandedQueries: ["béton"],
        keywords: ["béton"],
        context: "",
        domain: "construction" as const
      }

      const unrankedResults = [
        {
          id: '1',
          content: 'Some content about concrete',
          documentName: 'general.pdf',
          pageNumber: 1,
          similarity: 0.7,
          matchType: 'semantic' as const,
          relevanceScore: 0.7,
          sourceAttribution: { fileName: 'general.pdf', page: 1 },
          extractedPassages: ['concrete content']
        },
        {
          id: '2',
          content: 'Exact béton match here',
          documentName: 'cctp.pdf',
          pageNumber: 5,
          similarity: 0.6,
          matchType: 'exact' as const,
          relevanceScore: 0.6,
          sourceAttribution: { fileName: 'cctp.pdf', page: 5 },
          extractedPassages: ['béton match']
        }
      ]

      const ranked = advancedSearch['rankResults'](unrankedResults, query)
      
      // Exact match with CCTP document should rank higher despite lower initial similarity
      expect(ranked[0].matchType).toBe('exact')
      expect(ranked[0].documentName).toBe('cctp.pdf')
      expect(ranked[0].relevanceScore).toBeGreaterThan(0.6) // Should be boosted
    })
  })

  describe('Passage Extraction', () => {
    it('should extract relevant passages with highlighting', () => {
      const content = "La configuration des couleurs du système permet de personnaliser l'interface utilisateur selon les besoins du projet."
      const searchTerm = "configuration couleurs"

      const passages = advancedSearch['extractRelevantPassages'](content, searchTerm)
      
      expect(passages).toHaveLength(1)
      expect(passages[0]).toContain('**configuration couleurs**')
      expect(passages[0]).toContain('système permet')
    })

    it('should handle multiple occurrences', () => {
      const content = "La couleur rouge est utilisée. La couleur bleue aussi. Les couleurs sont importantes."
      const searchTerm = "couleur"

      const passages = advancedSearch['extractRelevantPassages'](content, searchTerm)
      
      expect(passages.length).toBeGreaterThan(1)
      passages.forEach(passage => {
        expect(passage).toContain('**couleur**')
      })
    })
  })

  describe('Regex Pattern Creation', () => {
    it('should create flexible regex patterns', () => {
      const term = "béton"
      const pattern = advancedSearch['createRegexPattern'](term)
      
      expect(pattern).toBe('\\bbéton\\w*\\b')
      
      // Test that it would match variations
      const regex = new RegExp(pattern, 'i')
      expect(regex.test('béton')).toBe(true)
      expect(regex.test('bétons')).toBe(true)
      expect(regex.test('bétonnage')).toBe(true)
    })

    it('should escape special regex characters', () => {
      const term = "C25/30"
      const pattern = advancedSearch['createRegexPattern'](term)
      
      expect(pattern).toBe('\\bC25\\/30\\w*\\b')
    })
  })
})

describe('Integration with Chat System', () => {
  it('should provide precise source attribution format', () => {
    const mockResult = {
      id: '1',
      content: 'La configuration des couleurs se trouve dans le fichier config.json',
      documentName: 'guide-technique.pdf',
      pageNumber: 23,
      section: 'Configuration',
      similarity: 0.95,
      matchType: 'exact' as const,
      relevanceScore: 0.95,
      sourceAttribution: {
        fileName: 'guide-technique.pdf',
        page: 23,
        section: 'Configuration'
      },
      extractedPassages: ['La **configuration des couleurs** se trouve dans le fichier config.json']
    }

    // Verify the result has all required fields for precise attribution
    expect(mockResult.documentName).toBeDefined()
    expect(mockResult.pageNumber).toBeDefined()
    expect(mockResult.section).toBeDefined()
    expect(mockResult.extractedPassages).toBeDefined()
    expect(mockResult.sourceAttribution.fileName).toBeDefined()
    expect(mockResult.sourceAttribution.page).toBeDefined()
  })
})
