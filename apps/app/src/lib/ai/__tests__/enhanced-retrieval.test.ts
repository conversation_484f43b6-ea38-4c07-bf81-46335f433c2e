import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { DocumentChunker } from '../chunking'
import { semanticSearchInDocumentChunks } from '../embeddings'
import { prisma } from '@/lib/prisma'

describe('Enhanced Document Retrieval', () => {
  const testProjectId = 'test-project-id'
  const testDocumentId = 'test-document-id'

  beforeEach(async () => {
    // Clean up any existing test data
    await prisma.documentChunk.deleteMany({
      where: { projectDocumentId: testDocumentId }
    })
    await prisma.projectDocument.deleteMany({
      where: { id: testDocumentId }
    })
    await prisma.project.deleteMany({
      where: { id: testProjectId }
    })
  })

  afterEach(async () => {
    // Clean up test data
    await prisma.documentChunk.deleteMany({
      where: { projectDocumentId: testDocumentId }
    })
    await prisma.projectDocument.deleteMany({
      where: { id: testDocumentId }
    })
    await prisma.project.deleteMany({
      where: { id: testProjectId }
    })
  })

  describe('Multi-Strategy Chunking', () => {
    it('should create multiple chunking strategies', () => {
      const chunker = new DocumentChunker()
      const testText = `
        Article 1: Spécifications techniques
        Les matériaux utilisés doivent respecter la norme NF EN 206.
        Le béton C25/30 sera utilisé pour les fondations.
        
        Article 2: Délais d'exécution
        Les travaux débuteront le 15 mars 2024.
        La durée prévisionnelle est de 6 mois.
      `

      const chunks = chunker.createMultipleChunkingStrategies(testText, 'test.txt')
      
      // Should have chunks from different strategies
      const strategies = new Set(chunks.map(chunk => chunk.metadata.strategy))
      expect(strategies.has('standard')).toBe(true)
      expect(strategies.has('granular')).toBe(true)
      expect(strategies.has('sentence')).toBe(true)
      
      // Should have more chunks than standard chunking alone
      const standardChunks = chunks.filter(chunk => chunk.metadata.strategy === 'standard')
      const granularChunks = chunks.filter(chunk => chunk.metadata.strategy === 'granular')
      const sentenceChunks = chunks.filter(chunk => chunk.metadata.strategy === 'sentence')
      
      expect(granularChunks.length).toBeGreaterThan(0)
      expect(sentenceChunks.length).toBeGreaterThan(0)
      expect(chunks.length).toBeGreaterThan(standardChunks.length)
    })

    it('should create sentence-level chunks with context', () => {
      const chunker = new DocumentChunker()
      const testText = "Première phrase. Deuxième phrase importante. Troisième phrase finale."

      const sentenceChunks = chunker['createSentenceChunks'](testText, 'test.txt')
      
      expect(sentenceChunks.length).toBeGreaterThan(0)
      
      // Check that middle sentence has context from both sides
      const middleChunk = sentenceChunks.find(chunk => 
        chunk.content.includes('Deuxième phrase importante')
      )
      expect(middleChunk).toBeDefined()
      expect(middleChunk?.content).toContain('Première phrase')
      expect(middleChunk?.content).toContain('Troisième phrase')
    })
  })

  describe('Enhanced Semantic Search', () => {
    it('should handle exact phrase matching', async () => {
      // This test would require a full database setup with embeddings
      // For now, we'll test the ranking logic conceptually
      
      const mockChunks = [
        {
          id: '1',
          content: 'Le béton C25/30 sera utilisé pour les fondations selon la norme NF EN 206',
          similarity: 0.8,
          metadata: { strategy: 'sentence' },
          documentId: testDocumentId,
          originalFileName: 'cctp.pdf'
        },
        {
          id: '2', 
          content: 'Les matériaux de construction doivent être conformes',
          similarity: 0.7,
          metadata: { strategy: 'standard' },
          documentId: testDocumentId,
          originalFileName: 'cctp.pdf'
        }
      ]

      // Test ranking logic
      const query = 'béton C25/30'
      const rankedResults = mockChunks.map(chunk => {
        let score = chunk.similarity
        
        // Boost for sentence strategy
        if (chunk.metadata.strategy === 'sentence') {
          score += 0.1
        }
        
        // Boost for exact phrase match
        if (chunk.content.toLowerCase().includes(query.toLowerCase())) {
          score += 0.2
        }
        
        return { ...chunk, enhancedScore: score }
      }).sort((a, b) => b.enhancedScore - a.enhancedScore)

      expect(rankedResults[0].id).toBe('1') // Should rank first due to exact match + sentence strategy
      expect(rankedResults[0].enhancedScore).toBeGreaterThan(rankedResults[1].enhancedScore)
    })
  })

  describe('Memory Management', () => {
    it('should handle large text processing without memory issues', () => {
      const chunker = new DocumentChunker()
      
      // Create a large text document (simulating a large PDF)
      const largeText = 'Lorem ipsum dolor sit amet. '.repeat(10000) // ~270KB
      
      expect(() => {
        const chunks = chunker.createMultipleChunkingStrategies(largeText, 'large-doc.pdf')
        expect(chunks.length).toBeGreaterThan(0)
      }).not.toThrow()
    })

    it('should respect chunk size limits', () => {
      const chunker = new DocumentChunker({
        maxChunkSize: 100,
        overlapSize: 20,
        minChunkSize: 30
      })
      
      const text = 'A'.repeat(500) // 500 characters
      const chunks = chunker.chunkText(text, 'test.txt')
      
      // All chunks should respect size limits
      chunks.forEach(chunk => {
        expect(chunk.content.length).toBeLessThanOrEqual(100)
        expect(chunk.content.length).toBeGreaterThanOrEqual(30)
      })
    })
  })
})

describe('Memory Usage Monitoring', () => {
  it('should track memory usage during processing', () => {
    const initialMemory = process.memoryUsage()
    
    // Simulate processing
    const largeArray = new Array(1000000).fill('test')
    const processedMemory = process.memoryUsage()
    
    expect(processedMemory.heapUsed).toBeGreaterThan(initialMemory.heapUsed)
    
    // Clean up
    largeArray.length = 0
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc()
    }
  })
})
