import { prisma } from "@/lib/prisma"
import { logger } from "@buildismart/lib"

// Types for targeted document search
export interface DocumentIdentification {
  documentId: string
  documentName: string
  documentType: string
  confidence: number
  matchReason: string
}

export interface ExhaustiveSearchResult {
  documentId: string
  documentName: string
  totalChunks: number
  searchedChunks: number
  matches: DocumentMatch[]
  searchQuery: string
  searchType: "exact" | "fuzzy" | "comprehensive"
}

export interface DocumentMatch {
  chunkId: string
  content: string
  pageNumber: number
  chunkIndex: number
  matchScore: number
  matchType: "exact" | "partial" | "contextual"
  extractedText: string
  contextBefore?: string
  contextAfter?: string
  highlightedContent: string
}

/**
 * Targeted Document Search System
 * Provides exhaustive search capabilities within specific documents
 */
export class TargetedDocumentSearch {
  /**
   * Identify documents based on user query and context
   */
  async identifyTargetDocuments(projectId: string, query: string, context?: string): Promise<DocumentIdentification[]> {
    logger.log("Identifying target documents", { projectId, query, context })

    // Get all documents in the project
    const documents = await prisma.projectDocument.findMany({
      where: {
        projectId,
        status: "READY",
      },
      select: {
        id: true,
        originalFileName: true,
        mimeType: true,
        extractedText: true,
      },
    })

    const identifications: DocumentIdentification[] = []

    // Document type mappings for construction industry
    const documentTypePatterns = {
      CCTP: ["cctp", "cahier des clauses techniques particulières", "clauses techniques"],
      DPGF: ["dpgf", "détail quantitatif estimatif", "devis quantitatif"],
      Plans: ["plan", "dwg", "pdf plan", "architecture", "structure"],
      Devis: ["devis", "estimation", "prix", "coût", "budget"],
      DTU: ["dtu", "document technique unifié", "norme"],
      Rapport: ["rapport", "étude", "analyse", "diagnostic"],
      Manuel: ["manuel", "guide", "notice", "mode d'emploi"],
      Spécifications: ["spécification", "spec", "technique", "caractéristique"],
    }

    for (const doc of documents) {
      let confidence = 0
      let matchReason = ""
      let documentType = "Document"

      const fileName = doc.originalFileName.toLowerCase()
      const queryLower = query.toLowerCase()

      // Direct filename matching
      if (fileName.includes(queryLower) || queryLower.includes(fileName.replace(/\.[^/.]+$/, ""))) {
        confidence += 0.9
        matchReason += "Nom de fichier correspondant. "
      }

      // Document type identification
      for (const [type, patterns] of Object.entries(documentTypePatterns)) {
        for (const pattern of patterns) {
          if (fileName.includes(pattern) || queryLower.includes(pattern)) {
            confidence += 0.7
            documentType = type
            matchReason += `Type de document identifié: ${type}. `
            break
          }
        }
      }

      // Content-based identification (sample first 1000 chars)
      if (doc.extractedText) {
        const contentSample = doc.extractedText.substring(0, 1000).toLowerCase()
        const queryWords = queryLower.split(/\s+/).filter((word) => word.length > 2)

        let contentMatches = 0
        for (const word of queryWords) {
          if (contentSample.includes(word)) {
            contentMatches++
          }
        }

        if (contentMatches > 0) {
          const contentScore = (contentMatches / queryWords.length) * 0.5
          confidence += contentScore
          matchReason += `Contenu pertinent trouvé (${contentMatches}/${queryWords.length} mots). `
        }
      }

      // Context-based boosting
      if (context) {
        const contextLower = context.toLowerCase()
        if (fileName.includes(contextLower) || contextLower.includes(fileName.replace(/\.[^/.]+$/, ""))) {
          confidence += 0.3
          matchReason += "Contexte correspondant. "
        }
      }

      if (confidence > 0.2) {
        // Minimum threshold
        identifications.push({
          documentId: doc.id,
          documentName: doc.originalFileName,
          documentType,
          confidence: Math.min(confidence, 1.0),
          matchReason: matchReason.trim(),
        })
      }
    }

    // Sort by confidence
    identifications.sort((a, b) => b.confidence - a.confidence)

    logger.log("Document identification completed", {
      projectId,
      query,
      identificationsCount: identifications.length,
      topMatches: identifications.slice(0, 3).map((id) => ({
        name: id.documentName,
        confidence: id.confidence,
      })),
    })

    return identifications
  }

  /**
   * Perform exhaustive search within a specific document
   */
  async searchWithinDocument(
    documentId: string,
    searchQuery: string,
    searchType: "exact" | "fuzzy" | "comprehensive" = "comprehensive"
  ): Promise<ExhaustiveSearchResult> {
    logger.log("Starting exhaustive document search", { documentId, searchQuery, searchType })

    // Get document info
    const document = await prisma.projectDocument.findUnique({
      where: { id: documentId },
      select: {
        id: true,
        originalFileName: true,
      },
    })

    if (!document) {
      throw new Error(`Document not found: ${documentId}`)
    }

    // Get ALL chunks for this document
    const allChunks = await prisma.documentChunk.findMany({
      where: {
        projectDocumentId: documentId,
      },
      orderBy: [{ pageNumber: "asc" }, { id: "asc" }],
    })

    logger.log("Retrieved all chunks for document", {
      documentId,
      documentName: document.originalFileName,
      totalChunks: allChunks.length,
    })

    const matches: DocumentMatch[] = []
    const searchQueryLower = searchQuery.toLowerCase()

    // Search through ALL chunks
    for (let i = 0; i < allChunks.length; i++) {
      const chunk = allChunks[i]
      const content = chunk.content
      const contentLower = content.toLowerCase()

      let matchScore = 0
      let matchType: "exact" | "partial" | "contextual" = "contextual"
      let extractedText = ""
      let highlightedContent = content

      if (searchType === "exact" || searchType === "comprehensive") {
        // Exact phrase matching
        if (contentLower.includes(searchQueryLower)) {
          matchScore = 1.0
          matchType = "exact"
          extractedText = this.extractAroundMatch(content, searchQuery)
          highlightedContent = this.highlightMatches(content, searchQuery)
        }
      }

      if (searchType === "fuzzy" || searchType === "comprehensive") {
        // Fuzzy/partial matching
        const queryWords = searchQueryLower.split(/\s+/).filter((word) => word.length > 2)
        let wordMatches = 0

        for (const word of queryWords) {
          if (contentLower.includes(word)) {
            wordMatches++
          }
        }

        if (wordMatches > 0 && matchScore === 0) {
          matchScore = (wordMatches / queryWords.length) * 0.8
          matchType = wordMatches === queryWords.length ? "partial" : "contextual"
          extractedText = this.extractAroundWords(content, queryWords)
          highlightedContent = this.highlightWords(content, queryWords)
        }
      }

      // Include matches above threshold
      if (matchScore > 0.3) {
        // Get context from adjacent chunks
        const contextBefore = i > 0 ? allChunks[i - 1].content.substring(0, 200) : undefined
        const contextAfter = i < allChunks.length - 1 ? allChunks[i + 1].content.substring(0, 200) : undefined

        matches.push({
          chunkId: chunk.id,
          content: chunk.content,
          pageNumber: chunk.pageNumber || 1,
          chunkIndex: i,
          matchScore,
          matchType,
          extractedText,
          contextBefore,
          contextAfter,
          highlightedContent,
        })
      }
    }

    // Sort matches by score and page number
    matches.sort((a, b) => {
      if (Math.abs(a.matchScore - b.matchScore) < 0.1) {
        return a.pageNumber - b.pageNumber
      }
      return b.matchScore - a.matchScore
    })

    const result: ExhaustiveSearchResult = {
      documentId,
      documentName: document.originalFileName,
      totalChunks: allChunks.length,
      searchedChunks: allChunks.length,
      matches,
      searchQuery,
      searchType,
    }

    logger.log("Exhaustive document search completed", {
      documentId,
      documentName: document.originalFileName,
      totalChunks: allChunks.length,
      matchesFound: matches.length,
      topMatches: matches.slice(0, 3).map((m) => ({
        page: m.pageNumber,
        score: m.matchScore,
        type: m.matchType,
      })),
    })

    logger.log(allChunks)

    return result
  }

  /**
   * Extract text around a specific match
   */
  private extractAroundMatch(content: string, searchTerm: string, contextLength: number = 150): string {
    const contentLower = content.toLowerCase()
    const searchLower = searchTerm.toLowerCase()
    const index = contentLower.indexOf(searchLower)

    if (index === -1) return content.substring(0, contextLength)

    const start = Math.max(0, index - contextLength)
    const end = Math.min(content.length, index + searchTerm.length + contextLength)

    return content.substring(start, end)
  }

  /**
   * Extract text around multiple words
   */
  private extractAroundWords(content: string, words: string[], contextLength: number = 200): string {
    const contentLower = content.toLowerCase()
    let earliestIndex = content.length
    let latestIndex = 0

    for (const word of words) {
      const index = contentLower.indexOf(word)
      if (index !== -1) {
        earliestIndex = Math.min(earliestIndex, index)
        latestIndex = Math.max(latestIndex, index + word.length)
      }
    }

    if (earliestIndex === content.length) return content.substring(0, contextLength)

    const start = Math.max(0, earliestIndex - contextLength / 2)
    const end = Math.min(content.length, latestIndex + contextLength / 2)

    return content.substring(start, end)
  }

  /**
   * Highlight matches in content
   */
  private highlightMatches(content: string, searchTerm: string): string {
    const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "gi")
    return content.replace(regex, `**${searchTerm}**`)
  }

  /**
   * Highlight multiple words in content
   */
  private highlightWords(content: string, words: string[]): string {
    let highlighted = content
    for (const word of words) {
      if (word.length > 2) {
        const regex = new RegExp(`\\b${word.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`, "gi")
        highlighted = highlighted.replace(regex, `**${word}**`)
      }
    }
    return highlighted
  }

  /**
   * Search for specific information types (numbers, dates, specifications)
   */
  async searchSpecificInformation(
    documentId: string,
    informationType: "numbers" | "dates" | "specifications" | "prices" | "dimensions",
    context?: string
  ): Promise<DocumentMatch[]> {
    const allChunks = await prisma.documentChunk.findMany({
      where: { projectDocumentId: documentId },
      orderBy: [{ pageNumber: "asc" }, { id: "asc" }],
    })

    const patterns = {
      numbers: /\b\d+(?:[.,]\d+)*\s*(?:m|cm|mm|kg|t|l|m²|m³|%|€|°C)?\b/g,
      dates:
        /\b(?:\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}|\d{1,2}\s+(?:janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)\s+\d{4})\b/gi,
      specifications: /\b(?:C\d+\/\d+|NF\s+EN\s+\d+|DTU\s+\d+|classe\s+[A-Z]\d*)\b/gi,
      prices: /\b\d+(?:[.,]\d+)*\s*€(?:\/[a-zA-Z²³]+)?\b/g,
      dimensions: /\b\d+(?:[.,]\d+)*\s*(?:x\s*\d+(?:[.,]\d+)*)*\s*(?:m|cm|mm)\b/g,
    }

    const matches: DocumentMatch[] = []
    const pattern = patterns[informationType]

    for (let i = 0; i < allChunks.length; i++) {
      const chunk = allChunks[i]
      const content = chunk.content
      const foundMatches = content.match(pattern)

      if (foundMatches) {
        // Filter by context if provided
        let isRelevant = true
        if (context) {
          const contextLower = context.toLowerCase()
          const contentLower = content.toLowerCase()
          isRelevant = contextLower.split(/\s+/).some((word) => word.length > 2 && contentLower.includes(word))
        }

        if (isRelevant) {
          matches.push({
            chunkId: chunk.id,
            content: chunk.content,
            pageNumber: chunk.pageNumber || 1,
            chunkIndex: i,
            matchScore: 0.9,
            matchType: "exact",
            extractedText: foundMatches.join(", "),
            highlightedContent: this.highlightSpecificMatches(content, foundMatches),
          })
        }
      }
    }

    return matches
  }

  /**
   * Highlight specific pattern matches
   */
  private highlightSpecificMatches(content: string, matches: string[]): string {
    let highlighted = content
    for (const match of matches) {
      const regex = new RegExp(match.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "g")
      highlighted = highlighted.replace(regex, `**${match}**`)
    }
    return highlighted
  }
}
